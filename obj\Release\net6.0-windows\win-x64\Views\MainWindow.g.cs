﻿#pragma checksum "..\..\..\..\..\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4332224A1A82C4CE40369E9395038FC6D7926DDC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HaoZip.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 105 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenButton;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExtractButton;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestButton;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CompressionLevelCombo;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView FolderTreeView;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentPathLabel;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FileListGrid;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileCountText;
        
        #line default
        #line hidden
        
        
        #line 476 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSizeText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HaoZip;V1.0.0.0;component/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\..\Views\MainWindow.xaml"
            ((HaoZip.Views.MainWindow)(target)).Drop += new System.Windows.DragEventHandler(this.MainWindow_Drop);
            
            #line default
            #line hidden
            
            #line 10 "..\..\..\..\..\Views\MainWindow.xaml"
            ((HaoZip.Views.MainWindow)(target)).DragOver += new System.Windows.DragEventHandler(this.MainWindow_DragOver);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 72 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.NewArchive_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 73 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenArchive_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 75 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AddFiles_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 76 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AddFolder_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 78 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ExtractAll_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 79 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ExtractSelected_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 81 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.TestArchive_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 83 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Exit_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 86 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectAll_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 87 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.InvertSelection_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 88 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSelected_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 91 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 92 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SystemIntegration_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 95 "..\..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.About_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.NewButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\..\Views\MainWindow.xaml"
            this.NewButton.Click += new System.Windows.RoutedEventHandler(this.NewArchive_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.OpenButton = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\..\Views\MainWindow.xaml"
            this.OpenButton.Click += new System.Windows.RoutedEventHandler(this.OpenArchive_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.AddButton = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\..\Views\MainWindow.xaml"
            this.AddButton.Click += new System.Windows.RoutedEventHandler(this.AddFiles_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ExtractButton = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\..\..\Views\MainWindow.xaml"
            this.ExtractButton.Click += new System.Windows.RoutedEventHandler(this.ExtractAll_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TestButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\..\Views\MainWindow.xaml"
            this.TestButton.Click += new System.Windows.RoutedEventHandler(this.TestArchive_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.CompressionLevelCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.FolderTreeView = ((System.Windows.Controls.TreeView)(target));
            
            #line 247 "..\..\..\..\..\Views\MainWindow.xaml"
            this.FolderTreeView.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.FolderTreeView_SelectedItemChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            this.CurrentPathLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.FileListGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 329 "..\..\..\..\..\Views\MainWindow.xaml"
            this.FileListGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FileListGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 330 "..\..\..\..\..\Views\MainWindow.xaml"
            this.FileListGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.FileListGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            
            #line 331 "..\..\..\..\..\Views\MainWindow.xaml"
            this.FileListGrid.KeyDown += new System.Windows.Input.KeyEventHandler(this.FileListGrid_KeyDown);
            
            #line default
            #line hidden
            
            #line 332 "..\..\..\..\..\Views\MainWindow.xaml"
            this.FileListGrid.MouseMove += new System.Windows.Input.MouseEventHandler(this.FileListGrid_MouseMove);
            
            #line default
            #line hidden
            
            #line 333 "..\..\..\..\..\Views\MainWindow.xaml"
            this.FileListGrid.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.FileListGrid_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 25:
            this.EmptyStatePanel = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.FileCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.TotalSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

