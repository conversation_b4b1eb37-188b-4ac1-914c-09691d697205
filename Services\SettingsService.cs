using System;
using System.IO;
using System.Text.Json;
using Microsoft.Win32;
using HaoZip.Models;

namespace HaoZip.Services
{
    public class SettingsService : ISettingsService
    {
        private const string RegistryKey = @"SOFTWARE\HaoZip";
        private const string SettingsFileName = "settings.json";
        private readonly string _settingsPath;
        private CompressionSettings _cachedSettings;

        public SettingsService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "HaoZip");
            
            if (!Directory.Exists(appFolder))
            {
                Directory.CreateDirectory(appFolder);
            }
            
            _settingsPath = Path.Combine(appFolder, SettingsFileName);
        }

        public CompressionSettings LoadSettings()
        {
            if (_cachedSettings != null)
            {
                return _cachedSettings;
            }

            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    _cachedSettings = JsonSerializer.Deserialize<CompressionSettings>(json) ?? new CompressionSettings();
                }
                else
                {
                    _cachedSettings = new CompressionSettings();
                    SaveSettings(_cachedSettings);
                }
            }
            catch (Exception)
            {
                _cachedSettings = new CompressionSettings();
            }

            return _cachedSettings;
        }

        public void SaveSettings(CompressionSettings settings)
        {
            try
            {
                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                File.WriteAllText(_settingsPath, json);
                _cachedSettings = settings;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to save settings: {ex.Message}", ex);
            }
        }

        public T GetSetting<T>(string key, T defaultValue)
        {
            try
            {
                using var regKey = Registry.CurrentUser.OpenSubKey(RegistryKey);
                if (regKey != null)
                {
                    var value = regKey.GetValue(key);
                    if (value != null)
                    {
                        return (T)Convert.ChangeType(value, typeof(T));
                    }
                }
            }
            catch (Exception)
            {
            }

            return defaultValue;
        }

        public void SetSetting<T>(string key, T value)
        {
            try
            {
                using var regKey = Registry.CurrentUser.CreateSubKey(RegistryKey);
                regKey?.SetValue(key, value);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to save registry setting: {ex.Message}", ex);
            }
        }
    }
}
