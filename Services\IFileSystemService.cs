using System.Collections.Generic;

namespace HaoZip.Services
{
    public interface IFileSystemService
    {
        bool FileExists(string path);
        bool DirectoryExists(string path);
        long GetFileSize(string path);
        string GetFileName(string path);
        string GetDirectoryName(string path);
        string GetFileExtension(string path);
        List<string> GetFiles(string directory, string searchPattern = "*", bool includeSubdirectories = false);
        List<string> GetDirectories(string directory);
        void CreateDirectory(string path);
        void DeleteFile(string path);
        void DeleteDirectory(string path, bool recursive = false);
        string GetTempFileName();
        string GetTempPath();
    }
}
