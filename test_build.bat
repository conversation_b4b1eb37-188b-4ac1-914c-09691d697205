@echo off
echo Testing HaoZip compilation...
echo.

echo Building Debug version...
dotnet build --configuration Debug --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Debug build failed!
    exit /b 1
)

echo.
echo ✅ Debug build successful!

echo.
echo Building Release version...
dotnet build --configuration Release --verbosity minimal

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Release build failed!
    exit /b 1
)

echo.
echo ✅ Release build successful!
echo.
echo 🎉 All builds completed successfully!
echo.
echo Debug build location: bin\Debug\net6.0-windows\
echo Release build location: bin\Release\net6.0-windows\
