using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using HaoZip.Models;
using HaoZip.Services;
using HaoZip.Views.Dialogs;

namespace HaoZip.Views
{
    public partial class MainWindow : Window
    {
        private readonly ICompressionService _compressionService;
        private readonly ISettingsService _settingsService;
        private readonly ObservableCollection<ArchiveEntry> _archiveEntries;
        private readonly ObservableCollection<ArchiveEntry> _currentFolderEntries;
        private readonly VirtualFileSystem _virtualFileSystem;
        private TempFileManager _tempFileManager;
        private string _currentArchivePath;
        private string _currentFolderPath = "/";
        private CancellationTokenSource _cancellationTokenSource;
        private Point _dragStartPoint;
        private bool _isDragging;

        public MainWindow()
        {
            InitializeComponent();

            _compressionService = ServiceLocator.GetService<ICompressionService>();
            _settingsService = ServiceLocator.GetService<ISettingsService>();
            _archiveEntries = new ObservableCollection<ArchiveEntry>();
            _currentFolderEntries = new ObservableCollection<ArchiveEntry>();
            _virtualFileSystem = new VirtualFileSystem();
            _tempFileManager = new TempFileManager();

            // 绑定数据源
            FileListGrid.ItemsSource = _currentFolderEntries;

            LoadSettings();
            UpdateUI();
        }

        private void LoadSettings()
        {
            var settings = _settingsService.LoadSettings();
            CompressionLevelCombo.SelectedIndex = (int)settings.DefaultLevel;
        }

        private void UpdateUI()
        {
            var hasArchive = !string.IsNullOrEmpty(_currentArchivePath);
            var hasEntries = _archiveEntries.Count > 0;
            var hasCurrentFolderEntries = _currentFolderEntries.Count > 0;
            var hasSelection = FileListGrid.SelectedItems.Count > 0;

            ExtractButton.IsEnabled = hasEntries;
            CopyButton.IsEnabled = hasSelection && hasArchive;
            TestButton.IsEnabled = hasArchive;
            AddButton.IsEnabled = hasArchive;
            BackButton.IsEnabled = hasArchive && _currentFolderPath != "/";

            EmptyStatePanel.Visibility = hasCurrentFolderEntries ? Visibility.Collapsed : Visibility.Visible;

            FileCountText.Text = $"{_currentFolderEntries.Count} 个文件";
            TotalSizeText.Text = FormatFileSize(_currentFolderEntries.Sum(e => e.Size));

            Title = hasArchive ? $"🎮 HaoZip复古街机 - {Path.GetFileName(_currentArchivePath).ToUpper()} 🎮" : "🎮 HaoZip复古街机 🎮";

            // 更新当前路径显示
            CurrentPathLabel.Text = _currentFolderPath;
        }

        /// <summary>
        /// 刷新当前文件夹视图
        /// </summary>
        private void RefreshCurrentFolderView()
        {
            _currentFolderEntries.Clear();

            if (_virtualFileSystem.RootNode != null)
            {
                var entriesInCurrentFolder = _virtualFileSystem.GetEntriesInPath(_currentFolderPath, _archiveEntries.ToList());
                foreach (var entry in entriesInCurrentFolder)
                {
                    _currentFolderEntries.Add(entry);
                }
            }

            UpdateUI();
        }

        /// <summary>
        /// 文件夹树选择改变事件
        /// </summary>
        private void FolderTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is FolderTreeNode selectedNode)
            {
                _currentFolderPath = selectedNode.FullPath;
                RefreshCurrentFolderView();
            }
        }

        /// <summary>
        /// 返回上级目录按钮点击事件
        /// </summary>
        private void BackButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentFolderPath != "/")
            {
                // 获取父目录路径
                var parentPath = Path.GetDirectoryName(_currentFolderPath.TrimEnd('/'))?.Replace('\\', '/');
                if (string.IsNullOrEmpty(parentPath) || parentPath == ".")
                {
                    parentPath = "/";
                }

                _currentFolderPath = parentPath;
                RefreshCurrentFolderView();

                // 在树形视图中选中对应的节点
                var node = _virtualFileSystem.FindNode(_currentFolderPath);
                if (node != null)
                {
                    node.IsSelected = true;
                }
            }
        }

        /// <summary>
        /// 文件列表键盘事件
        /// </summary>
        private void FileListGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.C && Keyboard.Modifiers == ModifierKeys.Control)
            {
                CopySelectedFiles();
                e.Handled = true;
            }
            else if (e.Key == Key.Enter)
            {
                if (FileListGrid.SelectedItem is ArchiveEntry selectedEntry)
                {
                    ExtractAndOpenFile(selectedEntry);
                }
                e.Handled = true;
            }
        }

        /// <summary>
        /// 复制选中的文件到剪贴板
        /// </summary>
        private async void CopySelectedFiles()
        {
            var selectedEntries = FileListGrid.SelectedItems.Cast<ArchiveEntry>().ToList();
            if (!selectedEntries.Any())
            {
                StatusText.Text = "没有选中的文件";
                MessageBox.Show("请先选择要复制的文件。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                StatusText.Text = "没有打开的压缩包";
                MessageBox.Show("请先打开一个压缩包。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                StatusText.Text = $"正在复制 {selectedEntries.Count} 个文件...";

                // 显示进度对话框
                var progressDialog = new ProgressDialog();
                progressDialog.Owner = this;
                progressDialog.Show();

                // 创建进度报告
                var progress = new Progress<ProgressInfo>(info =>
                {
                    progressDialog.UpdateProgress(info);
                    StatusText.Text = info.Status ?? "正在处理...";
                });

                // 获取选中文件的完整路径
                var entryPaths = selectedEntries.Select(e => e.FullPath).ToList();

                // 解压文件到临时目录
                var tempFiles = await _tempFileManager.ExtractFilesToTempAsync(
                    _compressionService,
                    _currentArchivePath,
                    entryPaths,
                    progress);

                progressDialog.Close();

                if (tempFiles.Any())
                {
                    // 复制到剪贴板
                    _tempFileManager.CopyFilesToClipboard(tempFiles);
                    StatusText.Text = $"已复制 {tempFiles.Count} 个文件到剪贴板";

                    // 显示成功消息
                    MessageBox.Show($"成功复制 {tempFiles.Count} 个文件到剪贴板！\n\n您现在可以在任何地方粘贴这些文件。",
                        "复制成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusText.Text = "没有文件被复制";
                    MessageBox.Show("没有文件被成功复制。", "复制失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "复制失败";
                MessageBox.Show($"复制文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 鼠标按下事件 - 记录拖拽起始点
        /// </summary>
        private void FileListGrid_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _dragStartPoint = e.GetPosition(null);
            _isDragging = false;
        }

        /// <summary>
        /// 鼠标移动事件 - 处理拖拽操作
        /// </summary>
        private async void FileListGrid_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && !_isDragging && FileListGrid.SelectedItems.Count > 0)
            {
                Point mousePos = e.GetPosition(null);
                Vector diff = _dragStartPoint - mousePos;

                // 增加拖拽阈值，避免意外触发
                var dragThreshold = Math.Max(SystemParameters.MinimumHorizontalDragDistance * 2, 10);

                if (Math.Abs(diff.X) > dragThreshold || Math.Abs(diff.Y) > dragThreshold)
                {
                    _isDragging = true;
                    await StartDragOperation();
                }
            }
        }

        /// <summary>
        /// 开始拖拽操作
        /// </summary>
        private async Task StartDragOperation()
        {
            var selectedEntries = FileListGrid.SelectedItems.Cast<ArchiveEntry>().ToList();
            if (!selectedEntries.Any() || string.IsNullOrEmpty(_currentArchivePath))
            {
                _isDragging = false;
                return;
            }

            try
            {
                StatusText.Text = $"正在准备拖拽 {selectedEntries.Count} 个文件...";

                // 获取选中文件的完整路径
                var entryPaths = selectedEntries.Select(e => e.FullPath).ToList();

                // 解压文件到临时目录（不显示进度对话框，避免干扰拖拽）
                var tempFiles = await _tempFileManager.ExtractFilesToTempAsync(
                    _compressionService,
                    _currentArchivePath,
                    entryPaths);

                if (tempFiles.Any())
                {
                    // 创建拖拽数据对象
                    var dataObject = _tempFileManager.CreateDataObject(tempFiles);
                    if (dataObject != null)
                    {
                        StatusText.Text = $"开始拖拽 {tempFiles.Count} 个文件...";

                        // 开始拖拽操作
                        var result = DragDrop.DoDragDrop(FileListGrid, dataObject, DragDropEffects.Copy);

                        if (result == DragDropEffects.Copy)
                        {
                            StatusText.Text = $"成功拖拽了 {tempFiles.Count} 个文件";
                        }
                        else
                        {
                            StatusText.Text = "拖拽操作已取消";
                        }
                    }
                }
                else
                {
                    StatusText.Text = "没有文件可以拖拽";
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "拖拽失败";
                MessageBox.Show($"拖拽操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isDragging = false;
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public void HandleFileArgument(string filePath)
        {
            if (_compressionService.IsArchiveFile(filePath))
            {
                OpenArchiveFile(filePath);
            }
        }

        private async void OpenArchiveFile(string filePath)
        {
            try
            {
                StatusText.Text = "正在加载压缩包...";
                _currentArchivePath = filePath;

                var entries = await _compressionService.ListArchiveContentsAsync(filePath);

                _archiveEntries.Clear();
                foreach (var entry in entries)
                {
                    _archiveEntries.Add(entry);
                }

                // 构建文件夹树
                var archiveName = Path.GetFileNameWithoutExtension(filePath);
                _virtualFileSystem.BuildFromArchiveEntries(entries, archiveName);

                // 设置树形视图数据源
                FolderTreeView.ItemsSource = new[] { _virtualFileSystem.RootNode };

                // 重置当前文件夹路径并刷新文件列表
                _currentFolderPath = "/";
                RefreshCurrentFolderView();

                StatusText.Text = $"已加载 {entries.Count} 个条目";
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open archive: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "就绪";
            }
        }

        private void NewArchive_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new SaveFileDialog
            {
                Filter = "ZIP 压缩包 (*.zip)|*.zip|TAR 压缩包 (*.tar)|*.tar|所有文件 (*.*)|*.*",
                DefaultExt = ".zip"
            };

            if (dialog.ShowDialog() == true)
            {
                _currentArchivePath = dialog.FileName;
                _archiveEntries.Clear();
                UpdateUI();
                StatusText.Text = $"新建压缩包: {Path.GetFileName(_currentArchivePath)}";
            }
        }

        private void OpenArchive_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "压缩文件 (*.zip;*.tar;*.gz;*.bz2)|*.zip;*.tar;*.gz;*.bz2|所有文件 (*.*)|*.*",
                Multiselect = false
            };

            if (dialog.ShowDialog() == true)
            {
                OpenArchiveFile(dialog.FileName);
            }
        }

        private async void AddFiles_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                MessageBox.Show("请先创建或打开一个压缩包。", "没有压缩包", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var dialog = new OpenFileDialog
            {
                Multiselect = true,
                Filter = "所有文件 (*.*)|*.*"
            };

            if (dialog.ShowDialog() == true)
            {
                await CompressFiles(dialog.FileNames.ToList());
            }
        }

        private async void AddFolder_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                MessageBox.Show("请先创建或打开一个压缩包。", "没有压缩包", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var selectedPath = Utils.FolderBrowserHelper.SelectFolder("选择要添加到压缩包的文件夹");
            if (!string.IsNullOrEmpty(selectedPath))
            {
                await CompressFiles(new List<string> { selectedPath });
            }
        }

        private async Task CompressFiles(List<string> filePaths)
        {
            var progressDialog = new ProgressDialog();
            progressDialog.Owner = this;

            _cancellationTokenSource = new CancellationTokenSource();

            // 订阅取消事件
            progressDialog.CancelRequested += (s, e) => _cancellationTokenSource?.Cancel();

            progressDialog.Show();

            var request = new CompressionRequest
            {
                SourcePaths = filePaths,
                DestinationPath = _currentArchivePath,
                Format = GetSelectedFormat(),
                Level = GetSelectedCompressionLevel(),
                IncludeSubdirectories = true
            };

            var progress = new Progress<ProgressInfo>(info =>
            {
                progressDialog.UpdateProgress(info);
            });

            try
            {
                var success = await _compressionService.CompressAsync(request, progress, _cancellationTokenSource.Token);
                
                if (success)
                {
                    await RefreshArchiveContents();
                    StatusText.Text = "压缩完成";
                }
                else
                {
                    StatusText.Text = "压缩失败或已取消";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"压缩失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "压缩失败";
            }
            finally
            {
                progressDialog.Close();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        private async Task RefreshArchiveContents()
        {
            if (!string.IsNullOrEmpty(_currentArchivePath) && File.Exists(_currentArchivePath))
            {
                var entries = await _compressionService.ListArchiveContentsAsync(_currentArchivePath);
                
                _archiveEntries.Clear();
                foreach (var entry in entries)
                {
                    _archiveEntries.Add(entry);
                }
                
                UpdateUI();
            }
        }

        private CompressionFormat GetSelectedFormat()
        {
            var extension = Path.GetExtension(_currentArchivePath).ToLowerInvariant();
            return extension switch
            {
                ".zip" => CompressionFormat.Zip,
                ".tar" => CompressionFormat.Tar,
                ".gz" => CompressionFormat.GZip,
                ".bz2" => CompressionFormat.BZip2,
                _ => CompressionFormat.Zip
            };
        }

        private CompressionLevel GetSelectedCompressionLevel()
        {
            return (CompressionLevel)CompressionLevelCombo.SelectedIndex;
        }

        private async void ExtractAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentArchivePath))
                {
                    MessageBox.Show("当前没有打开的压缩包。", "没有压缩包", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 验证压缩包文件是否存在
                if (!File.Exists(_currentArchivePath))
                {
                    MessageBox.Show($"压缩包文件不存在: {_currentArchivePath}", "文件不存在", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedPath = Utils.FolderBrowserHelper.SelectFolder("选择解压目标文件夹");
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    // 记录调试信息
                    System.Diagnostics.Debug.WriteLine($"开始解压: {_currentArchivePath} -> {selectedPath}");
                    await ExtractArchive(selectedPath, new List<string>());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"解压操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"ExtractAll_Click异常: {ex}");
            }
        }

        private async void ExtractSelected_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedEntries = _archiveEntries.Where(entry => entry.IsSelected).ToList();
                if (!selectedEntries.Any())
                {
                    MessageBox.Show("请选择要解压的文件。", "没有选择", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (string.IsNullOrEmpty(_currentArchivePath) || !File.Exists(_currentArchivePath))
                {
                    MessageBox.Show("压缩包文件不存在或无效。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedPath = Utils.FolderBrowserHelper.SelectFolder("选择选中文件的解压目标文件夹");
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    var selectedPaths = selectedEntries.Select(e => e.FullPath).ToList();
                    System.Diagnostics.Debug.WriteLine($"解压选中文件: {string.Join(", ", selectedPaths)} -> {selectedPath}");
                    await ExtractArchive(selectedPath, selectedPaths);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"解压选中文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"ExtractSelected_Click异常: {ex}");
            }
        }

        private async Task ExtractArchive(string destinationPath, List<string> selectedEntries)
        {
            try
            {
                // 验证输入参数
                if (string.IsNullOrEmpty(_currentArchivePath))
                {
                    MessageBox.Show("当前没有打开的压缩包。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrEmpty(destinationPath))
                {
                    MessageBox.Show("目标路径不能为空。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var progressDialog = new ProgressDialog();
                progressDialog.Owner = this;

                _cancellationTokenSource = new CancellationTokenSource();

                // 订阅取消事件
                progressDialog.CancelRequested += (s, e) =>
                {
                    try
                    {
                        _cancellationTokenSource?.Cancel();
                    }
                    catch (Exception ex)
                    {
                        // 忽略取消时的异常
                        System.Diagnostics.Debug.WriteLine($"取消操作时发生异常: {ex.Message}");
                    }
                };

                progressDialog.Show();

                var request = new ExtractionRequest
                {
                    ArchivePath = _currentArchivePath,
                    DestinationPath = destinationPath,
                    SelectedEntries = selectedEntries ?? new List<string>(),
                    OverwriteExisting = false
                };

                var progress = new Progress<ProgressInfo>(info =>
                {
                    try
                    {
                        progressDialog.UpdateProgress(info);
                    }
                    catch (Exception ex)
                    {
                        // 忽略进度更新时的异常
                        System.Diagnostics.Debug.WriteLine($"更新进度时发生异常: {ex.Message}");
                    }
                });

                StatusText.Text = "正在解压...";

                var success = await _compressionService.ExtractAsync(request, progress, _cancellationTokenSource.Token);

                if (success)
                {
                    StatusText.Text = "解压完成";

                    // 根据设置决定行为
                    var settings = _settingsService.LoadSettings();

                    if (settings.AlwaysOpenFolderAfterExtraction)
                    {
                        // 直接打开文件夹，不询问
                        OpenFolderInExplorer(destinationPath);
                        MessageBox.Show("解压完成！文件夹已自动打开。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else if (settings.AskToOpenFolderAfterExtraction)
                    {
                        // 使用标准MessageBox询问用户
                        var result = MessageBox.Show(
                            $"解压完成！\n\n文件已解压到：\n{destinationPath}\n\n是否打开目标文件夹？",
                            "解压成功",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Information);

                        if (result == MessageBoxResult.Yes)
                        {
                            OpenFolderInExplorer(destinationPath);
                        }
                    }
                    else
                    {
                        // 只显示成功消息，不打开文件夹
                        MessageBox.Show("解压完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    if (_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        StatusText.Text = "解压已取消";
                    }
                    else
                    {
                        StatusText.Text = "解压失败";
                        MessageBox.Show("解压失败，请检查压缩包是否损坏或目标路径是否有写入权限。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }

                progressDialog.Close();
            }
            catch (OperationCanceledException)
            {
                StatusText.Text = "解压已取消";
            }
            catch (Exception ex)
            {
                StatusText.Text = "解压失败";
                MessageBox.Show($"解压失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                try
                {
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"清理资源时发生异常: {ex.Message}");
                }
            }
        }

        private void TestArchive_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                MessageBox.Show("No archive is currently open.", "No Archive", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var isValid = _compressionService.TestArchive(_currentArchivePath);
            var message = isValid ? "Archive test passed successfully." : "Archive test failed - the archive may be corrupted.";
            var icon = isValid ? MessageBoxImage.Information : MessageBoxImage.Warning;
            
            MessageBox.Show(message, "Archive Test", MessageBoxButton.OK, icon);
        }

        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var entry in _currentFolderEntries)
            {
                entry.IsSelected = true;
            }
        }

        private void InvertSelection_Click(object sender, RoutedEventArgs e)
        {
            foreach (var entry in _currentFolderEntries)
            {
                entry.IsSelected = !entry.IsSelected;
            }
        }

        private void DeleteSelected_Click(object sender, RoutedEventArgs e)
        {
            var selectedEntries = _currentFolderEntries.Where(entry => entry.IsSelected).ToList();
            if (!selectedEntries.Any())
            {
                MessageBox.Show("请选择要删除的文件。", "没有选择", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"确定要从压缩包中删除 {selectedEntries.Count} 个选中的项目吗？",
                "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                foreach (var entry in selectedEntries)
                {
                    _archiveEntries.Remove(entry);
                    _currentFolderEntries.Remove(entry);
                }
                UpdateUI();
            }
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            var settingsDialog = new SettingsDialog();
            settingsDialog.Owner = this;
            settingsDialog.ShowDialog();
        }

        private void SystemIntegration_Click(object sender, RoutedEventArgs e)
        {
            var integrationDialog = new SystemIntegrationDialog();
            integrationDialog.Owner = this;
            integrationDialog.ShowDialog();
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutDialog = new AboutDialog();
            aboutDialog.Owner = this;
            aboutDialog.ShowDialog();
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void FileListGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateUI();
        }

        private void FileListGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (FileListGrid.SelectedItem is ArchiveEntry selectedEntry)
            {
                if (selectedEntry.IsDirectory)
                {
                    // 双击文件夹，进入该文件夹
                    _currentFolderPath = selectedEntry.FullPath.TrimEnd('/');
                    if (!_currentFolderPath.StartsWith("/"))
                        _currentFolderPath = "/" + _currentFolderPath;

                    RefreshCurrentFolderView();

                    // 在树形视图中选中对应的节点
                    var node = _virtualFileSystem.FindNode(_currentFolderPath);
                    if (node != null)
                    {
                        node.IsSelected = true;
                        node.IsExpanded = true;
                    }
                }
                else
                {
                    // 双击文件，预览文件
                    ExtractAndOpenFile(selectedEntry);
                }
            }
        }

        private async void ExtractAndOpenFile(ArchiveEntry entry)
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var tempFile = Path.Combine(tempPath, entry.Name);

                var request = new ExtractionRequest
                {
                    ArchivePath = _currentArchivePath,
                    DestinationPath = tempPath,
                    SelectedEntries = new List<string> { entry.FullPath },
                    OverwriteExisting = true
                };

                var success = await _compressionService.ExtractAsync(request, null, CancellationToken.None);

                if (success && File.Exists(tempFile))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = tempFile,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenFolderInExplorer(string folderPath)
        {
            try
            {
                if (Directory.Exists(folderPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "explorer.exe",
                        Arguments = $"\"{folderPath}\"",
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show($"目标文件夹不存在：{folderPath}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件夹：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MainWindow_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                HandleDroppedFiles(files);
            }
        }

        private void MainWindow_DragOver(object sender, DragEventArgs e)
        {
            e.Effects = e.Data.GetDataPresent(DataFormats.FileDrop) ? DragDropEffects.Copy : DragDropEffects.None;
        }

        private async void HandleDroppedFiles(string[] files)
        {
            if (files.Length == 1 && _compressionService.IsArchiveFile(files[0]))
            {
                OpenArchiveFile(files[0]);
            }
            else if (!string.IsNullOrEmpty(_currentArchivePath))
            {
                await CompressFiles(files.ToList());
            }
            else
            {
                var result = MessageBox.Show("No archive is currently open. Would you like to create a new archive?", 
                    "Create New Archive", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    NewArchive_Click(null, null);
                    if (!string.IsNullOrEmpty(_currentArchivePath))
                    {
                        await CompressFiles(files.ToList());
                    }
                }
            }
        }

        /// <summary>
        /// 右键菜单 - 复制文件
        /// </summary>
        private void CopyFiles_Click(object sender, RoutedEventArgs e)
        {
            CopySelectedFiles();
        }

        /// <summary>
        /// 右键菜单 - 预览文件
        /// </summary>
        private void PreviewFile_Click(object sender, RoutedEventArgs e)
        {
            if (FileListGrid.SelectedItem is ArchiveEntry selectedEntry)
            {
                ExtractAndOpenFile(selectedEntry);
            }
            else
            {
                MessageBox.Show("请选择一个文件进行预览。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 右键菜单 - 文件属性
        /// </summary>
        private void FileProperties_Click(object sender, RoutedEventArgs e)
        {
            var selectedEntries = FileListGrid.SelectedItems.Cast<ArchiveEntry>().ToList();
            if (!selectedEntries.Any())
            {
                MessageBox.Show("请选择一个文件查看属性。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var entry = selectedEntries.First();
            var properties = $"文件名: {entry.Name}\n" +
                           $"完整路径: {entry.FullPath}\n" +
                           $"文件类型: {entry.Type}\n" +
                           $"原始大小: {entry.SizeFormatted}\n" +
                           $"压缩后大小: {entry.CompressedSizeFormatted}\n" +
                           $"压缩比率: {entry.CompressionRatio:F1}%\n" +
                           $"修改时间: {entry.ModifiedTime:yyyy-MM-dd HH:mm:ss}";

            MessageBox.Show(properties, $"文件属性 - {entry.Name}", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 清理临时文件管理器
                _tempFileManager?.Dispose();

                // 清理取消令牌
                _cancellationTokenSource?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"窗口关闭时清理资源发生异常: {ex.Message}");
            }

            base.OnClosed(e);
        }
    }
}
