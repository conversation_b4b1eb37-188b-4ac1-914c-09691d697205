using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using HaoZip.Models;
using HaoZip.Services;
using HaoZip.Views.Dialogs;

namespace HaoZip.Views
{
    public partial class MainWindow : Window
    {
        private readonly ICompressionService _compressionService;
        private readonly ISettingsService _settingsService;
        private readonly ObservableCollection<ArchiveEntry> _archiveEntries;
        private string _currentArchivePath;
        private CancellationTokenSource _cancellationTokenSource;

        public MainWindow()
        {
            InitializeComponent();
            
            _compressionService = ServiceLocator.GetService<ICompressionService>();
            _settingsService = ServiceLocator.GetService<ISettingsService>();
            _archiveEntries = new ObservableCollection<ArchiveEntry>();
            
            FileListGrid.ItemsSource = _archiveEntries;
            
            LoadSettings();
            UpdateUI();
        }

        private void LoadSettings()
        {
            var settings = _settingsService.LoadSettings();
            CompressionLevelCombo.SelectedIndex = (int)settings.DefaultLevel;
        }

        private void UpdateUI()
        {
            var hasArchive = !string.IsNullOrEmpty(_currentArchivePath);
            var hasEntries = _archiveEntries.Count > 0;
            var hasSelection = FileListGrid.SelectedItems.Count > 0;

            ExtractButton.IsEnabled = hasEntries;
            TestButton.IsEnabled = hasArchive;
            AddButton.IsEnabled = hasArchive;

            EmptyStatePanel.Visibility = hasEntries ? Visibility.Collapsed : Visibility.Visible;
            
            FileCountText.Text = $"{_archiveEntries.Count} 个文件";
            TotalSizeText.Text = FormatFileSize(_archiveEntries.Sum(e => e.Size));

            Title = hasArchive ? $"🎮 HaoZip复古街机 - {Path.GetFileName(_currentArchivePath).ToUpper()} 🎮" : "🎮 HaoZip复古街机 🎮";
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public void HandleFileArgument(string filePath)
        {
            if (_compressionService.IsArchiveFile(filePath))
            {
                OpenArchiveFile(filePath);
            }
        }

        private async void OpenArchiveFile(string filePath)
        {
            try
            {
                StatusText.Text = "正在加载压缩包...";
                _currentArchivePath = filePath;

                var entries = await _compressionService.ListArchiveContentsAsync(filePath);

                _archiveEntries.Clear();
                foreach (var entry in entries)
                {
                    _archiveEntries.Add(entry);
                }

                StatusText.Text = $"已加载 {entries.Count} 个条目";
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open archive: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "就绪";
            }
        }

        private void NewArchive_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new SaveFileDialog
            {
                Filter = "ZIP 压缩包 (*.zip)|*.zip|TAR 压缩包 (*.tar)|*.tar|所有文件 (*.*)|*.*",
                DefaultExt = ".zip"
            };

            if (dialog.ShowDialog() == true)
            {
                _currentArchivePath = dialog.FileName;
                _archiveEntries.Clear();
                UpdateUI();
                StatusText.Text = $"新建压缩包: {Path.GetFileName(_currentArchivePath)}";
            }
        }

        private void OpenArchive_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "压缩文件 (*.zip;*.tar;*.gz;*.bz2)|*.zip;*.tar;*.gz;*.bz2|所有文件 (*.*)|*.*",
                Multiselect = false
            };

            if (dialog.ShowDialog() == true)
            {
                OpenArchiveFile(dialog.FileName);
            }
        }

        private async void AddFiles_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                MessageBox.Show("请先创建或打开一个压缩包。", "没有压缩包", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var dialog = new OpenFileDialog
            {
                Multiselect = true,
                Filter = "所有文件 (*.*)|*.*"
            };

            if (dialog.ShowDialog() == true)
            {
                await CompressFiles(dialog.FileNames.ToList());
            }
        }

        private async void AddFolder_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                MessageBox.Show("请先创建或打开一个压缩包。", "没有压缩包", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var selectedPath = Utils.FolderBrowserHelper.SelectFolder("选择要添加到压缩包的文件夹");
            if (!string.IsNullOrEmpty(selectedPath))
            {
                await CompressFiles(new List<string> { selectedPath });
            }
        }

        private async Task CompressFiles(List<string> filePaths)
        {
            var progressDialog = new ProgressDialog();
            progressDialog.Owner = this;

            _cancellationTokenSource = new CancellationTokenSource();

            // 订阅取消事件
            progressDialog.CancelRequested += (s, e) => _cancellationTokenSource?.Cancel();

            progressDialog.Show();

            var request = new CompressionRequest
            {
                SourcePaths = filePaths,
                DestinationPath = _currentArchivePath,
                Format = GetSelectedFormat(),
                Level = GetSelectedCompressionLevel(),
                IncludeSubdirectories = true
            };

            var progress = new Progress<ProgressInfo>(info =>
            {
                progressDialog.UpdateProgress(info);
            });

            try
            {
                var success = await _compressionService.CompressAsync(request, progress, _cancellationTokenSource.Token);
                
                if (success)
                {
                    await RefreshArchiveContents();
                    StatusText.Text = "压缩完成";
                }
                else
                {
                    StatusText.Text = "压缩失败或已取消";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"压缩失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "压缩失败";
            }
            finally
            {
                progressDialog.Close();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        private async Task RefreshArchiveContents()
        {
            if (!string.IsNullOrEmpty(_currentArchivePath) && File.Exists(_currentArchivePath))
            {
                var entries = await _compressionService.ListArchiveContentsAsync(_currentArchivePath);
                
                _archiveEntries.Clear();
                foreach (var entry in entries)
                {
                    _archiveEntries.Add(entry);
                }
                
                UpdateUI();
            }
        }

        private CompressionFormat GetSelectedFormat()
        {
            var extension = Path.GetExtension(_currentArchivePath).ToLowerInvariant();
            return extension switch
            {
                ".zip" => CompressionFormat.Zip,
                ".tar" => CompressionFormat.Tar,
                ".gz" => CompressionFormat.GZip,
                ".bz2" => CompressionFormat.BZip2,
                _ => CompressionFormat.Zip
            };
        }

        private CompressionLevel GetSelectedCompressionLevel()
        {
            return (CompressionLevel)CompressionLevelCombo.SelectedIndex;
        }

        private async void ExtractAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentArchivePath))
                {
                    MessageBox.Show("当前没有打开的压缩包。", "没有压缩包", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 验证压缩包文件是否存在
                if (!File.Exists(_currentArchivePath))
                {
                    MessageBox.Show($"压缩包文件不存在: {_currentArchivePath}", "文件不存在", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedPath = Utils.FolderBrowserHelper.SelectFolder("选择解压目标文件夹");
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    // 记录调试信息
                    System.Diagnostics.Debug.WriteLine($"开始解压: {_currentArchivePath} -> {selectedPath}");
                    await ExtractArchive(selectedPath, new List<string>());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"解压操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"ExtractAll_Click异常: {ex}");
            }
        }

        private async void ExtractSelected_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedEntries = _archiveEntries.Where(entry => entry.IsSelected).ToList();
                if (!selectedEntries.Any())
                {
                    MessageBox.Show("请选择要解压的文件。", "没有选择", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (string.IsNullOrEmpty(_currentArchivePath) || !File.Exists(_currentArchivePath))
                {
                    MessageBox.Show("压缩包文件不存在或无效。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var selectedPath = Utils.FolderBrowserHelper.SelectFolder("选择选中文件的解压目标文件夹");
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    var selectedPaths = selectedEntries.Select(e => e.FullPath).ToList();
                    System.Diagnostics.Debug.WriteLine($"解压选中文件: {string.Join(", ", selectedPaths)} -> {selectedPath}");
                    await ExtractArchive(selectedPath, selectedPaths);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"解压选中文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"ExtractSelected_Click异常: {ex}");
            }
        }

        private async Task ExtractArchive(string destinationPath, List<string> selectedEntries)
        {
            try
            {
                // 验证输入参数
                if (string.IsNullOrEmpty(_currentArchivePath))
                {
                    MessageBox.Show("当前没有打开的压缩包。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrEmpty(destinationPath))
                {
                    MessageBox.Show("目标路径不能为空。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var progressDialog = new ProgressDialog();
                progressDialog.Owner = this;

                _cancellationTokenSource = new CancellationTokenSource();

                // 订阅取消事件
                progressDialog.CancelRequested += (s, e) =>
                {
                    try
                    {
                        _cancellationTokenSource?.Cancel();
                    }
                    catch (Exception ex)
                    {
                        // 忽略取消时的异常
                        System.Diagnostics.Debug.WriteLine($"取消操作时发生异常: {ex.Message}");
                    }
                };

                progressDialog.Show();

                var request = new ExtractionRequest
                {
                    ArchivePath = _currentArchivePath,
                    DestinationPath = destinationPath,
                    SelectedEntries = selectedEntries ?? new List<string>(),
                    OverwriteExisting = false
                };

                var progress = new Progress<ProgressInfo>(info =>
                {
                    try
                    {
                        progressDialog.UpdateProgress(info);
                    }
                    catch (Exception ex)
                    {
                        // 忽略进度更新时的异常
                        System.Diagnostics.Debug.WriteLine($"更新进度时发生异常: {ex.Message}");
                    }
                });

                StatusText.Text = "正在解压...";

                var success = await _compressionService.ExtractAsync(request, progress, _cancellationTokenSource.Token);

                if (success)
                {
                    StatusText.Text = "解压完成";

                    // 根据设置决定行为
                    var settings = _settingsService.LoadSettings();

                    if (settings.AlwaysOpenFolderAfterExtraction)
                    {
                        // 直接打开文件夹，不询问
                        OpenFolderInExplorer(destinationPath);
                        MessageBox.Show("解压完成！文件夹已自动打开。", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else if (settings.AskToOpenFolderAfterExtraction)
                    {
                        // 显示自定义对话框询问用户
                        var dialog = new ExtractionCompleteDialog(destinationPath);
                        dialog.Owner = this;

                        var result = dialog.ShowDialog();

                        if (result == true && dialog.ShouldOpenFolder)
                        {
                            OpenFolderInExplorer(destinationPath);
                        }

                        // 如果用户选择了记住选择，更新设置
                        if (dialog.RememberChoice)
                        {
                            if (dialog.ShouldOpenFolder)
                            {
                                settings.AlwaysOpenFolderAfterExtraction = true;
                                settings.AskToOpenFolderAfterExtraction = false;
                            }
                            else
                            {
                                settings.AskToOpenFolderAfterExtraction = false;
                            }
                            _settingsService.SaveSettings(settings);
                        }
                    }
                    else
                    {
                        // 只显示成功消息，不打开文件夹
                        MessageBox.Show("解压完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    if (_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        StatusText.Text = "解压已取消";
                    }
                    else
                    {
                        StatusText.Text = "解压失败";
                        MessageBox.Show("解压失败，请检查压缩包是否损坏或目标路径是否有写入权限。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }

                progressDialog.Close();
            }
            catch (OperationCanceledException)
            {
                StatusText.Text = "解压已取消";
            }
            catch (Exception ex)
            {
                StatusText.Text = "解压失败";
                MessageBox.Show($"解压失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                try
                {
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"清理资源时发生异常: {ex.Message}");
                }
            }
        }

        private void TestArchive_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentArchivePath))
            {
                MessageBox.Show("No archive is currently open.", "No Archive", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var isValid = _compressionService.TestArchive(_currentArchivePath);
            var message = isValid ? "Archive test passed successfully." : "Archive test failed - the archive may be corrupted.";
            var icon = isValid ? MessageBoxImage.Information : MessageBoxImage.Warning;
            
            MessageBox.Show(message, "Archive Test", MessageBoxButton.OK, icon);
        }

        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var entry in _archiveEntries)
            {
                entry.IsSelected = true;
            }
        }

        private void InvertSelection_Click(object sender, RoutedEventArgs e)
        {
            foreach (var entry in _archiveEntries)
            {
                entry.IsSelected = !entry.IsSelected;
            }
        }

        private void DeleteSelected_Click(object sender, RoutedEventArgs e)
        {
            var selectedEntries = _archiveEntries.Where(entry => entry.IsSelected).ToList();
            if (!selectedEntries.Any())
            {
                MessageBox.Show("Please select files to delete.", "No Selection", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"Are you sure you want to delete {selectedEntries.Count} selected items from the archive?", 
                "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                foreach (var entry in selectedEntries)
                {
                    _archiveEntries.Remove(entry);
                }
                UpdateUI();
            }
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            var settingsDialog = new SettingsDialog();
            settingsDialog.Owner = this;
            settingsDialog.ShowDialog();
        }

        private void SystemIntegration_Click(object sender, RoutedEventArgs e)
        {
            var integrationDialog = new SystemIntegrationDialog();
            integrationDialog.Owner = this;
            integrationDialog.ShowDialog();
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutDialog = new AboutDialog();
            aboutDialog.Owner = this;
            aboutDialog.ShowDialog();
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void FileListGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateUI();
        }

        private void FileListGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (FileListGrid.SelectedItem is ArchiveEntry entry && !entry.IsDirectory)
            {
                ExtractAndOpenFile(entry);
            }
        }

        private async void ExtractAndOpenFile(ArchiveEntry entry)
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var tempFile = Path.Combine(tempPath, entry.Name);

                var request = new ExtractionRequest
                {
                    ArchivePath = _currentArchivePath,
                    DestinationPath = tempPath,
                    SelectedEntries = new List<string> { entry.FullPath },
                    OverwriteExisting = true
                };

                var success = await _compressionService.ExtractAsync(request, null, CancellationToken.None);

                if (success && File.Exists(tempFile))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = tempFile,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenFolderInExplorer(string folderPath)
        {
            try
            {
                if (Directory.Exists(folderPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "explorer.exe",
                        Arguments = $"\"{folderPath}\"",
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show($"目标文件夹不存在：{folderPath}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件夹：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MainWindow_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                HandleDroppedFiles(files);
            }
        }

        private void MainWindow_DragOver(object sender, DragEventArgs e)
        {
            e.Effects = e.Data.GetDataPresent(DataFormats.FileDrop) ? DragDropEffects.Copy : DragDropEffects.None;
        }

        private async void HandleDroppedFiles(string[] files)
        {
            if (files.Length == 1 && _compressionService.IsArchiveFile(files[0]))
            {
                OpenArchiveFile(files[0]);
            }
            else if (!string.IsNullOrEmpty(_currentArchivePath))
            {
                await CompressFiles(files.ToList());
            }
            else
            {
                var result = MessageBox.Show("No archive is currently open. Would you like to create a new archive?", 
                    "Create New Archive", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    NewArchive_Click(null, null);
                    if (!string.IsNullOrEmpty(_currentArchivePath))
                    {
                        await CompressFiles(files.ToList());
                    }
                }
            }
        }
    }
}
