using System;
using System.IO;

namespace HaoZip.Services
{
    public class LoggingService : ILoggingService
    {
        private readonly string _logDirectory;
        private readonly string _logFileName;
        private readonly object _lockObject = new object();

        public LoggingService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            _logDirectory = Path.Combine(appDataPath, "HaoZip", "Logs");
            _logFileName = Path.Combine(_logDirectory, $"haozip_{DateTime.Now:yyyyMMdd}.log");
            
            EnsureLogDirectoryExists();
        }

        private void EnsureLogDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(_logDirectory))
                {
                    Directory.CreateDirectory(_logDirectory);

                    // 写入一条初始化日志
                    var initMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [Info] 日志系统初始化完成，日志目录: {_logDirectory}";
                    File.WriteAllText(_logFileName, initMessage + Environment.NewLine);
                }
            }
            catch (Exception ex)
            {
                // 如果无法创建日志目录，尝试使用临时目录
                try
                {
                    var tempLogDir = Path.Combine(Path.GetTempPath(), "HaoZip", "Logs");
                    if (!Directory.Exists(tempLogDir))
                    {
                        Directory.CreateDirectory(tempLogDir);
                    }

                    var tempLogFile = Path.Combine(tempLogDir, $"haozip_{DateTime.Now:yyyyMMdd}.log");
                    var errorMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [Warning] 无法创建默认日志目录 {_logDirectory}，使用临时目录 {tempLogDir}。错误: {ex.Message}";
                    File.WriteAllText(tempLogFile, errorMessage + Environment.NewLine);
                }
                catch
                {
                    // 如果连临时目录都无法创建，则静默失败
                }
            }
        }

        public void LogInfo(string message)
        {
            WriteLog(LogLevel.Info, message);
        }

        public void LogWarning(string message)
        {
            WriteLog(LogLevel.Warning, message);
        }

        public void LogError(string message, Exception exception = null)
        {
            var fullMessage = exception != null ? $"{message}\nException: {exception}" : message;
            WriteLog(LogLevel.Error, fullMessage);
        }

        public void LogDebug(string message)
        {
            WriteLog(LogLevel.Debug, message);
        }

        private void WriteLog(LogLevel level, string message)
        {
            try
            {
                lock (_lockObject)
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var logEntry = $"[{timestamp}] [{level}] {message}";
                    
                    File.AppendAllText(_logFileName, logEntry + Environment.NewLine);
                }
            }
            catch (Exception)
            {
            }
        }
    }
}
