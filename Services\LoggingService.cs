using System;
using System.IO;

namespace HaoZip.Services
{
    public class LoggingService : ILoggingService
    {
        private readonly string _logDirectory;
        private readonly string _logFileName;
        private readonly object _lockObject = new object();

        public LoggingService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            _logDirectory = Path.Combine(appDataPath, "HaoZip", "Logs");
            _logFileName = Path.Combine(_logDirectory, $"haozip_{DateTime.Now:yyyyMMdd}.log");
            
            EnsureLogDirectoryExists();
        }

        private void EnsureLogDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(_logDirectory))
                {
                    Directory.CreateDirectory(_logDirectory);
                }
            }
            catch (Exception)
            {
            }
        }

        public void LogInfo(string message)
        {
            WriteLog(LogLevel.Info, message);
        }

        public void LogWarning(string message)
        {
            WriteLog(LogLevel.Warning, message);
        }

        public void LogError(string message, Exception exception = null)
        {
            var fullMessage = exception != null ? $"{message}\nException: {exception}" : message;
            WriteLog(LogLevel.Error, fullMessage);
        }

        public void LogDebug(string message)
        {
            WriteLog(LogLevel.Debug, message);
        }

        private void WriteLog(LogLevel level, string message)
        {
            try
            {
                lock (_lockObject)
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var logEntry = $"[{timestamp}] [{level}] {message}";
                    
                    File.AppendAllText(_logFileName, logEntry + Environment.NewLine);
                }
            }
            catch (Exception)
            {
            }
        }
    }
}
