using System;
using System.Diagnostics;
using HaoZip.Services;

namespace HaoZip.Utils
{
    public class PerformanceMonitor : IDisposable
    {
        private readonly string _operationName;
        private readonly Stopwatch _stopwatch;
        private readonly ILoggingService _loggingService;
        private readonly long _initialMemory;

        public PerformanceMonitor(string operationName, ILoggingService loggingService = null)
        {
            _operationName = operationName;
            _loggingService = loggingService;
            _stopwatch = Stopwatch.StartNew();
            _initialMemory = GC.GetTotalMemory(false);
            
            _loggingService?.LogDebug($"Started operation: {_operationName}");
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            var finalMemory = GC.GetTotalMemory(false);
            var memoryDelta = finalMemory - _initialMemory;
            
            _loggingService?.LogDebug($"Completed operation: {_operationName} " +
                $"(Duration: {_stopwatch.ElapsedMilliseconds}ms, " +
                $"Memory delta: {FormatBytes(memoryDelta)})");
        }

        public TimeSpan ElapsedTime => _stopwatch.Elapsed;
        public long ElapsedMilliseconds => _stopwatch.ElapsedMilliseconds;

        private static string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = Math.Abs(bytes);
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            var sign = bytes < 0 ? "-" : "";
            return $"{sign}{len:0.##} {sizes[order]}";
        }
    }

    public static class PerformanceHelper
    {
        public static void OptimizeForLargeFiles()
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        public static bool IsLowMemory()
        {
            var availableMemory = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;
            
            return workingSet > 500 * 1024 * 1024;
        }

        public static void ForceGarbageCollection()
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }
    }
}
