<Window x:Class="HaoZip.Views.Dialogs.SimpleExtractionCompleteDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="解压完成"
        Height="200" Width="400"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False"
        Background="White">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="✅ 解压完成！"
                   FontSize="16" 
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"/>

        <!-- 消息 -->
        <TextBlock Grid.Row="1" 
                   Text="文件已成功解压到目标文件夹。"
                   FontSize="12"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"/>

        <!-- 文件夹路径 -->
        <TextBox Grid.Row="2" 
                 Name="FolderPathTextBox"
                 IsReadOnly="True"
                 FontSize="10"
                 Margin="0,0,0,10"
                 Background="#FFF0F0F0"/>

        <!-- 询问 -->
        <TextBlock Grid.Row="3"
                   Text="是否打开目标文件夹？"
                   FontSize="12"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"/>

        <!-- 记住选择 -->
        <CheckBox Grid.Row="4"
                  Name="RememberChoiceCheckBox"
                  Content="记住我的选择，下次不再询问"
                  FontSize="11"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Top"
                  Margin="0,5,0,0"/>

        <!-- 按钮 -->
        <StackPanel Grid.Row="5" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,15,0,0">
            
            <Button Name="OpenFolderButton"
                    Content="打开文件夹"
                    Width="100"
                    Height="30"
                    Click="OpenFolderButton_Click"
                    Margin="0,0,10,0"
                    Background="#FF4CAF50"
                    Foreground="White"/>
            
            <Button Name="CloseButton"
                    Content="关闭"
                    Width="80"
                    Height="30"
                    Click="CloseButton_Click"
                    Background="#FF757575"
                    Foreground="White"/>
        </StackPanel>
    </Grid>
</Window>
