<Window x:Class="HaoZip.Views.Dialogs.SystemIntegrationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="System Integration" 
        Height="400" Width="500"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" 
                   Text="Configure HaoZip integration with Windows system"
                   FontSize="14" FontWeight="SemiBold"/>

        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <GroupBox Header="Context Menu Integration" Margin="0,0,0,15" Padding="10">
                    <StackPanel>
                        <TextBlock Text="Add HaoZip options to Windows Explorer context menu:" 
                                   Margin="0,0,0,10" TextWrapping="Wrap"/>
                        
                        <CheckBox Name="CompressHereCheckBox" 
                                  Content="Add 'Compress with HaoZip' to file/folder context menu" 
                                  Margin="0,5"/>
                        <CheckBox Name="ExtractHereCheckBox" 
                                  Content="Add 'Extract Here' to archive context menu" 
                                  Margin="0,5"/>
                        <CheckBox Name="ExtractToFolderCheckBox" 
                                  Content="Add 'Extract to Folder' to archive context menu" 
                                  Margin="0,5"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <Button Name="InstallContextMenuButton" 
                                    Content="Install Context Menu" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="InstallContextMenu_Click" 
                                    Margin="0,0,10,0"/>
                            <Button Name="UninstallContextMenuButton" 
                                    Content="Uninstall Context Menu" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="UninstallContextMenu_Click"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="File Associations" Margin="0,0,0,15" Padding="10">
                    <StackPanel>
                        <TextBlock Text="Associate archive file types with HaoZip:" 
                                   Margin="0,0,0,10" TextWrapping="Wrap"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <CheckBox Content=".zip files" IsChecked="True" Margin="0,2"/>
                                <CheckBox Content=".7z files" IsChecked="True" Margin="0,2"/>
                                <CheckBox Content=".rar files" IsChecked="True" Margin="0,2"/>
                                <CheckBox Content=".tar files" IsChecked="True" Margin="0,2"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <CheckBox Content=".gz files" IsChecked="True" Margin="0,2"/>
                                <CheckBox Content=".bz2 files" IsChecked="True" Margin="0,2"/>
                                <CheckBox Content=".cab files" IsChecked="False" Margin="0,2"/>
                                <CheckBox Content=".iso files" IsChecked="False" Margin="0,2"/>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <Button Name="RegisterAssociationsButton" 
                                    Content="Register File Types" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="RegisterAssociations_Click" 
                                    Margin="0,0,10,0"/>
                            <Button Name="UnregisterAssociationsButton" 
                                    Content="Unregister File Types" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="UnregisterAssociations_Click"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="Default Program" Margin="0,0,0,15" Padding="10">
                    <StackPanel>
                        <TextBlock Text="Set HaoZip as the default program for archive files:" 
                                   Margin="0,0,0,10" TextWrapping="Wrap"/>
                        
                        <CheckBox Name="SetAsDefaultCheckBox" 
                                  Content="Set HaoZip as default archive program" 
                                  Margin="0,5"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <Button Name="SetDefaultButton" 
                                    Content="Set as Default" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="SetDefault_Click" 
                                    Margin="0,0,10,0"/>
                            <Button Name="OpenDefaultAppsButton" 
                                    Content="Open Default Apps Settings" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="OpenDefaultApps_Click"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="Status" Padding="10">
                    <StackPanel>
                        <TextBlock Name="StatusTextBlock" 
                                   Text="Click 'Check Status' to see current integration status" 
                                   TextWrapping="Wrap" Margin="0,0,0,10"/>
                        
                        <Button Name="CheckStatusButton" 
                                Content="Check Status" 
                                Style="{StaticResource ModernButtonStyle}"
                                Click="CheckStatus_Click" 
                                HorizontalAlignment="Left"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Name="CloseButton" Content="Close" Width="80" 
                    Style="{StaticResource ModernButtonStyle}" 
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
