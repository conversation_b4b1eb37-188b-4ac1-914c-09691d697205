# HaoZip - Professional Archive Manager

HaoZip是一个功能强大的Windows桌面压缩解压软件，支持多种压缩格式，提供完整的系统集成功能。

## 功能特性

### 核心功能
- **多格式支持**: ZIP、7Z、RAR、TAR、GZIP、BZIP2等主流压缩格式
- **密码保护**: 支持压缩时设置密码，解压时密码验证
- **压缩级别**: 提供从存储到超高压缩的多种压缩级别选择
- **批量操作**: 支持同时处理多个文件和文件夹

### 用户界面
- **现代化界面**: 基于WPF的现代化用户界面
- **拖拽支持**: 支持文件和文件夹的拖拽操作
- **进度显示**: 实时显示压缩/解压进度和状态信息
- **文件预览**: 在压缩包中预览文件内容

### 系统集成
- **右键菜单**: 集成到Windows资源管理器右键菜单
- **文件关联**: 支持设置为默认压缩软件
- **双击打开**: 双击压缩文件自动用HaoZip打开

### 高级功能
- **性能优化**: 针对大文件处理进行优化
- **错误处理**: 完善的错误处理和日志记录
- **设置管理**: 丰富的配置选项和个性化设置

## 技术架构

### 开发环境
- **.NET 6.0**: 基于.NET 6.0框架
- **WPF**: Windows Presentation Foundation用户界面
- **C#**: 主要开发语言

### 核心依赖
- **SharpCompress**: 压缩解压核心库
- **SevenZipSharp**: 7-Zip格式支持
- **Microsoft.Win32.Registry**: Windows注册表操作
- **System.Windows.Forms**: 文件夹选择对话框

### 项目结构
```
HaoZip/
├── Views/                  # 用户界面
│   ├── MainWindow.xaml     # 主窗口
│   └── Dialogs/            # 对话框
├── Services/               # 业务服务
│   ├── CompressionService  # 压缩解压服务
│   ├── SettingsService     # 设置管理服务
│   └── RegistryService     # 注册表服务
├── Models/                 # 数据模型
├── Utils/                  # 工具类
├── Styles/                 # 样式资源
└── Resources/              # 应用资源
```

## 构建和运行

### 前置要求
- Visual Studio 2022 或更高版本
- .NET 6.0 SDK
- Windows 10/11 操作系统

### 构建步骤
1. 克隆或下载项目源代码
2. 在Visual Studio中打开`HaoZip.csproj`
3. 还原NuGet包依赖
4. 编译项目（Build -> Build Solution）
5. 运行项目（Debug -> Start Debugging）

### 发布部署
```bash
dotnet publish -c Release -r win-x64 --self-contained true
```

## 使用说明

### 基本操作
1. **创建压缩包**: 文件 -> 新建压缩包，选择文件和压缩格式
2. **打开压缩包**: 文件 -> 打开压缩包，或直接拖拽压缩文件到窗口
3. **添加文件**: 在打开的压缩包中，使用"添加"按钮添加文件
4. **解压文件**: 选择文件后点击"解压"按钮，选择目标目录

### 系统集成
1. **安装右键菜单**: 工具 -> 系统集成 -> 安装右键菜单
2. **设置文件关联**: 工具 -> 系统集成 -> 注册文件类型
3. **设为默认程序**: 工具 -> 系统集成 -> 设为默认程序

### 高级设置
- **压缩设置**: 工具 -> 设置 -> 常规，配置默认压缩格式和级别
- **性能设置**: 工具 -> 设置 -> 常规，调整线程数和内存限制
- **集成设置**: 工具 -> 设置 -> 集成，配置系统集成选项

## 注意事项

### 权限要求
- 系统集成功能需要管理员权限
- 文件关联和右键菜单注册需要管理员权限
- 普通压缩解压操作不需要特殊权限

### 兼容性
- 支持Windows 10 1809及更高版本
- 支持Windows 11所有版本
- 需要.NET 6.0运行时（自包含部署除外）

### 性能建议
- 处理大文件时建议关闭其他占用内存的程序
- 压缩大量小文件时建议使用"快速"压缩级别
- 定期清理临时目录以释放磁盘空间

## 故障排除

### 常见问题
1. **无法打开压缩文件**: 检查文件是否损坏，尝试使用"测试压缩包"功能
2. **系统集成失败**: 确保以管理员身份运行程序
3. **压缩速度慢**: 降低压缩级别或增加线程数
4. **内存不足**: 关闭其他程序或降低内存限制设置

### 日志文件
应用程序日志保存在：
```
%APPDATA%\HaoZip\Logs\haozip_YYYYMMDD.log
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进HaoZip。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目主页: https://github.com/haozip/haozip
- 邮箱: <EMAIL>
