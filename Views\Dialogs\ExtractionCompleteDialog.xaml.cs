using System;
using System.Windows;
using HaoZip.Services;

namespace HaoZip.Views.Dialogs
{
    public partial class ExtractionCompleteDialog : Window
    {
        public bool ShouldOpenFolder { get; private set; }
        public bool RememberChoice { get; private set; }
        
        private readonly string _folderPath;

        public ExtractionCompleteDialog(string folderPath)
        {
            InitializeComponent();
            _folderPath = folderPath;
            FolderPathText.Text = folderPath;
        }

        private void OpenFolderButton_Click(object sender, RoutedEventArgs e)
        {
            ShouldOpenFolder = true;
            RememberChoice = RememberChoiceCheckBox.IsChecked == true;
            DialogResult = true;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            ShouldOpenFolder = false;
            RememberChoice = RememberChoiceCheckBox.IsChecked == true;
            DialogResult = false;
            Close();
        }
    }
}
