<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <!-- <ApplicationIcon>Resources\haozip.ico</ApplicationIcon> -->
    <AssemblyTitle>HaoZip复古街机 - 专业压缩管理器</AssemblyTitle>
    <AssemblyDescription>复古游戏风格的Windows压缩解压软件</AssemblyDescription>
    <AssemblyCompany>HaoZip Technologies</AssemblyCompany>
    <AssemblyProduct>HaoZip复古街机</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2025 HaoZip Technologies</Copyright>
    <StartupObject>HaoZip.App</StartupObject>

    <!-- 独立部署配置 - 开发时简化 -->
    <SelfContained>false</SelfContained>
    <!-- 发布时启用单文件 -->
    <!-- <RuntimeIdentifier>win-x64</RuntimeIdentifier> -->
    <!-- <PublishSingleFile>true</PublishSingleFile> -->
    <!-- <PublishReadyToRun>true</PublishReadyToRun> -->
    <!-- <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract> -->
    <!-- <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile> -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SharpCompress" Version="0.35.0" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
    <Folder Include="Controls\" />
  </ItemGroup>

</Project>
