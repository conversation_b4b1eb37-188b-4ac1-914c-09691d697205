using System;
using System.Windows;
using Microsoft.Win32;

namespace HaoZip.Utils
{
    public static class FolderBrowserHelper
    {
        public static string SelectFolder(string description = "Select folder", string initialPath = "")
        {
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = description,
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "Select Folder",
                    Filter = "Folders|*.folder",
                    ValidateNames = false
                };

                if (!string.IsNullOrEmpty(initialPath))
                {
                    dialog.InitialDirectory = initialPath;
                }

                // 使用文件夹选择的技巧
                dialog.FileName = "Select Folder";
                
                if (dialog.ShowDialog() == true)
                {
                    return System.IO.Path.GetDirectoryName(dialog.FileName);
                }
            }
            catch (Exception)
            {
                // 如果上面的方法失败，使用简单的输入对话框
                return ShowInputDialog(description, initialPath);
            }

            return null;
        }

        private static string ShowInputDialog(string prompt, string defaultValue = "")
        {
            var inputDialog = new Window
            {
                Title = "Select Folder",
                Width = 400,
                Height = 150,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                ResizeMode = ResizeMode.NoResize
            };

            var stackPanel = new System.Windows.Controls.StackPanel
            {
                Margin = new Thickness(20)
            };

            var label = new System.Windows.Controls.Label
            {
                Content = prompt
            };

            var textBox = new System.Windows.Controls.TextBox
            {
                Text = defaultValue,
                Margin = new Thickness(0, 10, 0, 10)
            };

            var buttonPanel = new System.Windows.Controls.StackPanel
            {
                Orientation = System.Windows.Controls.Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var okButton = new System.Windows.Controls.Button
            {
                Content = "OK",
                Width = 75,
                Margin = new Thickness(0, 0, 10, 0),
                IsDefault = true
            };

            var cancelButton = new System.Windows.Controls.Button
            {
                Content = "Cancel",
                Width = 75,
                IsCancel = true
            };

            string result = null;

            okButton.Click += (s, e) =>
            {
                result = textBox.Text;
                inputDialog.DialogResult = true;
            };

            cancelButton.Click += (s, e) =>
            {
                inputDialog.DialogResult = false;
            };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);

            stackPanel.Children.Add(label);
            stackPanel.Children.Add(textBox);
            stackPanel.Children.Add(buttonPanel);

            inputDialog.Content = stackPanel;

            if (inputDialog.ShowDialog() == true)
            {
                return result;
            }

            return null;
        }
    }
}
