using System;
using System.Windows;

namespace HaoZip.Views.Dialogs
{
    public partial class SimpleExtractionCompleteDialog : Window
    {
        public bool ShouldOpenFolder { get; private set; }
        public bool RememberChoice { get; private set; }
        
        private readonly string _folderPath;

        public SimpleExtractionCompleteDialog(string folderPath)
        {
            InitializeComponent();
            _folderPath = folderPath;
            FolderPathTextBox.Text = folderPath;
        }

        private void OpenFolderButton_Click(object sender, RoutedEventArgs e)
        {
            ShouldOpenFolder = true;
            RememberChoice = RememberChoiceCheckBox.IsChecked == true;
            DialogResult = true;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            ShouldOpenFolder = false;
            RememberChoice = RememberChoiceCheckBox.IsChecked == true;
            DialogResult = false;
            Close();
        }
    }
}
