using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using HaoZip.Models;
using HaoZip.Services;

namespace HaoZip.Views.Dialogs
{
    public partial class SettingsDialog : Window
    {
        private readonly ISettingsService _settingsService;
        private CompressionSettings _settings;

        public SettingsDialog()
        {
            InitializeComponent();

            _settingsService = ServiceLocator.GetService<ISettingsService>();
            _settings = _settingsService.LoadSettings();

            // 添加事件处理器确保复选框互斥
            AskOpenFolderCheckBox.Checked += AskOpenFolderCheckBox_Checked;
            AlwaysOpenFolderCheckBox.Checked += AlwaysOpenFolderCheckBox_Checked;

            LoadSettings();
        }

        private void LoadSettings()
        {
            DefaultFormatCombo.SelectedIndex = (int)_settings.DefaultFormat;
            DefaultLevelCombo.SelectedIndex = (int)_settings.DefaultLevel;
            TempDirectoryTextBox.Text = _settings.TempDirectory;
            DeleteSourceCheckBox.IsChecked = _settings.DeleteSourceAfterCompression;
            ShowProgressCheckBox.IsChecked = _settings.ShowProgressDialog;
            
            ThreadCountSlider.Value = Environment.ProcessorCount;
            ThreadCountLabel.Text = ThreadCountSlider.Value.ToString();
            
            MemoryLimitSlider.Value = 512;
            MemoryLimitLabel.Text = MemoryLimitSlider.Value.ToString();
            
            ExplorerIntegrationCheckBox.IsChecked = _settings.IntegrateWithExplorer;
            DefaultProgramCheckBox.IsChecked = _settings.SetAsDefaultProgram;

            // 解压后行为设置
            AskOpenFolderCheckBox.IsChecked = _settings.AskToOpenFolderAfterExtraction;
            AlwaysOpenFolderCheckBox.IsChecked = _settings.AlwaysOpenFolderAfterExtraction;

            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            LogDirectoryTextBox.Text = Path.Combine(appDataPath, "HaoZip", "Logs");
        }

        private void SaveSettings()
        {
            _settings.DefaultFormat = (CompressionFormat)DefaultFormatCombo.SelectedIndex;
            _settings.DefaultLevel = (CompressionLevel)DefaultLevelCombo.SelectedIndex;
            _settings.TempDirectory = TempDirectoryTextBox.Text;
            _settings.DeleteSourceAfterCompression = DeleteSourceCheckBox.IsChecked ?? false;
            _settings.ShowProgressDialog = ShowProgressCheckBox.IsChecked ?? true;
            _settings.IntegrateWithExplorer = ExplorerIntegrationCheckBox.IsChecked ?? true;
            _settings.SetAsDefaultProgram = DefaultProgramCheckBox.IsChecked ?? false;

            // 解压后行为设置
            _settings.AskToOpenFolderAfterExtraction = AskOpenFolderCheckBox.IsChecked ?? true;
            _settings.AlwaysOpenFolderAfterExtraction = AlwaysOpenFolderCheckBox.IsChecked ?? false;

            _settingsService.SaveSettings(_settings);
        }

        private void ThreadCountSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (ThreadCountLabel != null)
            {
                ThreadCountLabel.Text = ((int)e.NewValue).ToString();
            }
        }

        private void MemoryLimitSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (MemoryLimitLabel != null)
            {
                MemoryLimitLabel.Text = ((int)e.NewValue).ToString();
            }
        }

        private void BrowseTempDirectory_Click(object sender, RoutedEventArgs e)
        {
            var selectedPath = Utils.FolderBrowserHelper.SelectFolder("Select temporary directory", TempDirectoryTextBox.Text);
            if (!string.IsNullOrEmpty(selectedPath))
            {
                TempDirectoryTextBox.Text = selectedPath;
            }
        }

        private void BrowseLogDirectory_Click(object sender, RoutedEventArgs e)
        {
            var selectedPath = Utils.FolderBrowserHelper.SelectFolder("Select log directory", LogDirectoryTextBox.Text);
            if (!string.IsNullOrEmpty(selectedPath))
            {
                LogDirectoryTextBox.Text = selectedPath;
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to save settings: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                MessageBox.Show("Settings saved successfully.", "Settings",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to save settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AskOpenFolderCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (AskOpenFolderCheckBox.IsChecked == true)
            {
                AlwaysOpenFolderCheckBox.IsChecked = false;
            }
        }

        private void AlwaysOpenFolderCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (AlwaysOpenFolderCheckBox.IsChecked == true)
            {
                AskOpenFolderCheckBox.IsChecked = false;
            }
        }
    }
}
