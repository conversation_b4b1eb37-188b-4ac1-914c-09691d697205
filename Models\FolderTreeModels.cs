using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;

namespace HaoZip.Models
{
    /// <summary>
    /// 文件夹树节点模型
    /// </summary>
    public class FolderTreeNode : INotifyPropertyChanged
    {
        private bool _isExpanded;
        private bool _isSelected;
        private ObservableCollection<FolderTreeNode> _children;

        public FolderTreeNode()
        {
            _children = new ObservableCollection<FolderTreeNode>();
        }

        /// <summary>
        /// 文件夹名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; set; }

        /// <summary>
        /// 显示名称（带图标）
        /// </summary>
        public string DisplayName => IsRoot ? "📦 " + Name : "📁 " + Name;

        /// <summary>
        /// 是否为根节点
        /// </summary>
        public bool IsRoot { get; set; }

        /// <summary>
        /// 是否展开
        /// </summary>
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                _isExpanded = value;
                OnPropertyChanged(nameof(IsExpanded));
            }
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        /// <summary>
        /// 子文件夹
        /// </summary>
        public ObservableCollection<FolderTreeNode> Children
        {
            get => _children;
            set
            {
                _children = value;
                OnPropertyChanged(nameof(Children));
                OnPropertyChanged(nameof(HasChildren));
            }
        }

        /// <summary>
        /// 是否有子文件夹
        /// </summary>
        public bool HasChildren => _children?.Count > 0;

        /// <summary>
        /// 父节点
        /// </summary>
        public FolderTreeNode Parent { get; set; }

        /// <summary>
        /// 添加子节点
        /// </summary>
        public void AddChild(FolderTreeNode child)
        {
            child.Parent = this;
            _children.Add(child);
            OnPropertyChanged(nameof(HasChildren));
        }

        /// <summary>
        /// 查找子节点
        /// </summary>
        public FolderTreeNode FindChild(string name)
        {
            return _children.FirstOrDefault(c => c.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取所有子孙节点的完整路径
        /// </summary>
        public void GetAllDescendantPaths(ObservableCollection<string> paths)
        {
            foreach (var child in _children)
            {
                paths.Add(child.FullPath);
                child.GetAllDescendantPaths(paths);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 虚拟文件系统管理器
    /// </summary>
    public class VirtualFileSystem
    {
        private FolderTreeNode _rootNode;

        public FolderTreeNode RootNode
        {
            get => _rootNode;
            private set => _rootNode = value;
        }

        /// <summary>
        /// 从压缩包条目构建文件夹树
        /// </summary>
        public void BuildFromArchiveEntries(System.Collections.Generic.List<ArchiveEntry> entries, string archiveName)
        {
            _rootNode = new FolderTreeNode
            {
                Name = archiveName,
                FullPath = "/",
                IsRoot = true,
                IsExpanded = true
            };

            foreach (var entry in entries.Where(e => e.IsDirectory))
            {
                AddFolderToTree(entry.FullPath);
            }

            // 为包含文件但没有显式文件夹条目的路径创建文件夹
            foreach (var entry in entries.Where(e => !e.IsDirectory))
            {
                var folderPath = System.IO.Path.GetDirectoryName(entry.FullPath)?.Replace('\\', '/');
                if (!string.IsNullOrEmpty(folderPath))
                {
                    AddFolderToTree(folderPath);
                }
            }
        }

        /// <summary>
        /// 添加文件夹到树中
        /// </summary>
        private void AddFolderToTree(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath) || folderPath == "/")
                return;

            // 标准化路径
            folderPath = folderPath.Replace('\\', '/').Trim('/');
            var pathParts = folderPath.Split('/');
            var currentNode = _rootNode;
            var currentPath = "";

            foreach (var part in pathParts)
            {
                if (string.IsNullOrEmpty(part))
                    continue;

                currentPath = string.IsNullOrEmpty(currentPath) ? part : currentPath + "/" + part;
                
                var existingChild = currentNode.FindChild(part);
                if (existingChild == null)
                {
                    var newNode = new FolderTreeNode
                    {
                        Name = part,
                        FullPath = "/" + currentPath,
                        IsRoot = false
                    };
                    currentNode.AddChild(newNode);
                    currentNode = newNode;
                }
                else
                {
                    currentNode = existingChild;
                }
            }
        }

        /// <summary>
        /// 获取指定路径下的文件
        /// </summary>
        public System.Collections.Generic.List<ArchiveEntry> GetFilesInPath(
            string folderPath,
            System.Collections.Generic.List<ArchiveEntry> allEntries)
        {
            if (string.IsNullOrEmpty(folderPath) || folderPath == "/")
            {
                // 根目录：返回根级别的文件（不包含子文件夹中的文件）
                return allEntries.Where(e => !e.IsDirectory &&
                                           !e.FullPath.Contains('/'))
                                .ToList();
            }

            // 标准化路径
            folderPath = folderPath.Replace('\\', '/').Trim('/');

            // 返回指定文件夹下的直接文件（不包含子文件夹中的文件）
            return allEntries.Where(e => !e.IsDirectory)
                             .Where(e =>
                             {
                                 var entryDir = System.IO.Path.GetDirectoryName(e.FullPath)?.Replace('\\', '/');
                                 if (string.IsNullOrEmpty(entryDir))
                                     return false;

                                 return entryDir.Equals(folderPath, StringComparison.OrdinalIgnoreCase);
                             })
                             .ToList();
        }

        /// <summary>
        /// 查找节点
        /// </summary>
        public FolderTreeNode FindNode(string path)
        {
            if (string.IsNullOrEmpty(path) || path == "/")
                return _rootNode;

            path = path.Replace('\\', '/').Trim('/');
            var pathParts = path.Split('/');
            var currentNode = _rootNode;

            foreach (var part in pathParts)
            {
                if (string.IsNullOrEmpty(part))
                    continue;

                currentNode = currentNode.FindChild(part);
                if (currentNode == null)
                    return null;
            }

            return currentNode;
        }
    }
}
