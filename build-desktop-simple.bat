@echo off
echo Building HaoZip Desktop Application...
echo.

echo Step 1: Cleaning...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "Desktop" rmdir /s /q "Desktop"

echo Step 2: Restoring packages...
dotnet restore

echo Step 3: Publishing desktop app...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "Desktop"

echo.
if exist "Desktop\HaoZip.exe" (
    echo SUCCESS! Desktop application created.
    echo Location: Desktop\HaoZip.exe
    echo.
    set /p choice="Run HaoZip now? (y/n): "
    if /i "%choice%"=="y" (
        start "" "Desktop\HaoZip.exe"
    )
) else (
    echo ERROR: Build failed. Check the output above.
)

echo.
pause
