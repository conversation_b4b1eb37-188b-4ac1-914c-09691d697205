using System;
using System.IO;
using System.Windows;
using HaoZip.Services;

namespace HaoZip.Utils
{
    public static class ExceptionHandler
    {
        private static ILoggingService _loggingService;

        public static void Initialize(ILoggingService loggingService)
        {
            _loggingService = loggingService;
        }

        public static void HandleException(Exception ex, string context = null, bool showToUser = true)
        {
            var message = context != null ? $"{context}: {ex.Message}" : ex.Message;
            
            _loggingService?.LogError(message, ex);

            if (showToUser)
            {
                var userMessage = GetUserFriendlyMessage(ex, context);
                MessageBox.Show(userMessage, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static bool TryExecute(Action action, string context = null, bool showErrorToUser = true)
        {
            try
            {
                action();
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex, context, showErrorToUser);
                return false;
            }
        }

        public static T TryExecute<T>(Func<T> func, T defaultValue = default(T), string context = null, bool showErrorToUser = true)
        {
            try
            {
                return func();
            }
            catch (Exception ex)
            {
                HandleException(ex, context, showErrorToUser);
                return defaultValue;
            }
        }

        private static string GetUserFriendlyMessage(Exception ex, string context)
        {
            var baseMessage = context != null ? $"Error in {context}" : "An error occurred";

            return ex switch
            {
                UnauthorizedAccessException => $"{baseMessage}: Access denied. Please check file permissions or run as administrator.",
                FileNotFoundException => $"{baseMessage}: The specified file could not be found.",
                DirectoryNotFoundException => $"{baseMessage}: The specified directory could not be found.",
                IOException => $"{baseMessage}: File operation failed. The file may be in use by another program.",
                OutOfMemoryException => $"{baseMessage}: Not enough memory available. Try closing other applications.",
                OperationCanceledException => $"{baseMessage}: Operation was cancelled.",
                ArgumentException => $"{baseMessage}: Invalid input provided.",
                NotSupportedException => $"{baseMessage}: This operation is not supported.",
                _ => $"{baseMessage}: {ex.Message}"
            };
        }
    }
}
