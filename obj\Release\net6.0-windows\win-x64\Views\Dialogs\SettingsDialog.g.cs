﻿#pragma checksum "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "83BBCC0A997DE70C5B1F046DE7AAEE8E81B6E405"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HaoZip.Views.Dialogs {
    
    
    /// <summary>
    /// SettingsDialog
    /// </summary>
    public partial class SettingsDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DefaultFormatCombo;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DefaultLevelCombo;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TempDirectoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DeleteSourceCheckBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowProgressCheckBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ConfirmDeleteCheckBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoOpenArchiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ThreadCountSlider;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ThreadCountLabel;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MemoryLimitSlider;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryLimitLabel;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExplorerIntegrationCheckBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DefaultProgramCheckBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FileAssociationsPanel;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox VerboseLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogDirectoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberPasswordsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox WarnUnsafeOperationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ScanExtractedFilesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HaoZip;V1.0.0.0;component/views/dialogs/settingsdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DefaultFormatCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.DefaultLevelCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.TempDirectoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            
            #line 82 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseTempDirectory_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DeleteSourceCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.ShowProgressCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.ConfirmDeleteCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.AutoOpenArchiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.ThreadCountSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 113 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            this.ThreadCountSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ThreadCountSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ThreadCountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.MemoryLimitSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 122 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            this.MemoryLimitSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.MemoryLimitSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.MemoryLimitLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ExplorerIntegrationCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.DefaultProgramCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.StartupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.FileAssociationsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.EnableLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.VerboseLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.LogDirectoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            
            #line 200 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseLogDirectory_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.RememberPasswordsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.WarnUnsafeOperationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.ScanExtractedFilesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 223 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.ApplyButton = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\..\..\..\Views\Dialogs\SettingsDialog.xaml"
            this.ApplyButton.Click += new System.Windows.RoutedEventHandler(this.ApplyButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

