using System;
using System.Security;
using Microsoft.Win32;

namespace HaoZip.Services
{
    public class RegistryService : IRegistryService
    {
        private const string ClassesRoot = @"HKEY_CLASSES_ROOT";
        private const string CurrentUser = @"HKEY_CURRENT_USER";

        public void RegisterFileAssociation(string extension, string progId, string description, string iconPath, string commandPath)
        {
            try
            {
                using var extKey = Registry.ClassesRoot.CreateSubKey(extension);
                extKey?.SetValue("", progId);

                using var progIdKey = Registry.ClassesRoot.CreateSubKey(progId);
                progIdKey?.SetValue("", description);

                using var iconKey = progIdKey?.CreateSubKey("DefaultIcon");
                iconKey?.SetValue("", iconPath);

                using var shellKey = progIdKey?.CreateSubKey(@"shell\open\command");
                shellKey?.SetValue("", $"\"{commandPath}\" \"%1\"");
            }
            catch (SecurityException ex)
            {
                throw new UnauthorizedAccessException($"Administrator privileges required to register file association: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to register file association: {ex.Message}", ex);
            }
        }

        public void UnregisterFileAssociation(string extension, string progId)
        {
            try
            {
                Registry.ClassesRoot.DeleteSubKeyTree(extension, false);
                Registry.ClassesRoot.DeleteSubKeyTree(progId, false);
            }
            catch (SecurityException ex)
            {
                throw new UnauthorizedAccessException($"Administrator privileges required to unregister file association: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to unregister file association: {ex.Message}", ex);
            }
        }

        public void RegisterContextMenu(string fileType, string menuText, string commandPath)
        {
            try
            {
                var keyPath = $@"{fileType}\shell\{menuText}\command";
                using var key = Registry.ClassesRoot.CreateSubKey(keyPath);
                key?.SetValue("", $"\"{commandPath}\" \"%1\"");
            }
            catch (SecurityException ex)
            {
                throw new UnauthorizedAccessException($"Administrator privileges required to register context menu: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to register context menu: {ex.Message}", ex);
            }
        }

        public void UnregisterContextMenu(string fileType, string menuText)
        {
            try
            {
                var keyPath = $@"{fileType}\shell\{menuText}";
                Registry.ClassesRoot.DeleteSubKeyTree(keyPath, false);
            }
            catch (SecurityException ex)
            {
                throw new UnauthorizedAccessException($"Administrator privileges required to unregister context menu: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to unregister context menu: {ex.Message}", ex);
            }
        }

        public void SetAsDefaultProgram(string progId, string[] extensions)
        {
            try
            {
                foreach (var extension in extensions)
                {
                    using var key = Registry.CurrentUser.CreateSubKey($@"Software\Microsoft\Windows\CurrentVersion\Explorer\FileExts\{extension}\UserChoice");
                    key?.SetValue("Progid", progId);
                }
            }
            catch (SecurityException ex)
            {
                throw new UnauthorizedAccessException($"Administrator privileges required to set as default program: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to set as default program: {ex.Message}", ex);
            }
        }

        public bool IsRegisteredAsDefault(string extension)
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey($@"Software\Microsoft\Windows\CurrentVersion\Explorer\FileExts\{extension}\UserChoice");
                var progId = key?.GetValue("Progid") as string;
                return !string.IsNullOrEmpty(progId) && progId.Contains("HaoZip");
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
