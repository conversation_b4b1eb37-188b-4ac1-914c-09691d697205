<Window x:Class="HaoZip.Views.Dialogs.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="HaoZip Settings" 
        Height="500" Width="600"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TabControl Grid.Row="0" Margin="10">
            <TabItem Header="General">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <GroupBox Grid.Row="0" Header="Default Compression Settings" Padding="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Content="Default Format:" VerticalAlignment="Center"/>
                            <ComboBox Grid.Row="0" Grid.Column="1" Name="DefaultFormatCombo" SelectedIndex="0">
                                <ComboBoxItem Content="ZIP"/>
                                <ComboBoxItem Content="TAR"/>
                                <ComboBoxItem Content="GZIP"/>
                                <ComboBoxItem Content="BZIP2"/>
                            </ComboBox>

                            <Label Grid.Row="2" Grid.Column="0" Content="Default Level:" VerticalAlignment="Center"/>
                            <ComboBox Grid.Row="2" Grid.Column="1" Name="DefaultLevelCombo" SelectedIndex="3">
                                <ComboBoxItem Content="Store (No compression)"/>
                                <ComboBoxItem Content="Fastest"/>
                                <ComboBoxItem Content="Fast"/>
                                <ComboBoxItem Content="Normal"/>
                                <ComboBoxItem Content="Maximum"/>
                                <ComboBoxItem Content="Ultra"/>
                            </ComboBox>
                        </Grid>
                    </GroupBox>

                    <GroupBox Grid.Row="2" Header="Directories" Padding="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Column="0" Content="Temp Directory:" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" Name="TempDirectoryTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}" 
                                     VerticalAlignment="Center" 
                                     Margin="0,0,10,0"/>
                            <Button Grid.Column="2" Content="Browse..." 
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="BrowseTempDirectory_Click"/>
                        </Grid>
                    </GroupBox>

                    <GroupBox Grid.Row="4" Header="Behavior" Padding="10">
                        <StackPanel>
                            <CheckBox Name="DeleteSourceCheckBox" Content="Delete source files after compression" Margin="0,5"/>
                            <CheckBox Name="ShowProgressCheckBox" Content="Show progress dialog during operations" Margin="0,5" IsChecked="True"/>
                            <CheckBox Name="ConfirmDeleteCheckBox" Content="Confirm before deleting files" Margin="0,5" IsChecked="True"/>
                            <CheckBox Name="AutoOpenArchiveCheckBox" Content="Automatically open created archives" Margin="0,5"/>
                            <Separator Margin="0,10"/>
                            <TextBlock Text="解压后行为:" FontWeight="SemiBold" Margin="0,5,0,5"/>
                            <CheckBox Name="AskOpenFolderCheckBox" Content="解压完成后询问是否打开文件夹" Margin="0,2" IsChecked="True"/>
                            <CheckBox Name="AlwaysOpenFolderCheckBox" Content="解压完成后总是自动打开文件夹" Margin="0,2"/>
                        </StackPanel>
                    </GroupBox>

                    <GroupBox Grid.Row="6" Header="Performance" Padding="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Content="Thread Count:" VerticalAlignment="Center"/>
                            <Slider Grid.Row="0" Grid.Column="1" Name="ThreadCountSlider"
                                    Minimum="1" Maximum="16" Value="4"
                                    TickFrequency="1" IsSnapToTickEnabled="True"
                                    VerticalAlignment="Center" Margin="0,0,10,0"
                                    ValueChanged="ThreadCountSlider_ValueChanged"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Name="ThreadCountLabel"
                                       Text="4" VerticalAlignment="Center" Width="20"/>

                            <Label Grid.Row="2" Grid.Column="0" Content="Memory Limit (MB):" VerticalAlignment="Center"/>
                            <Slider Grid.Row="2" Grid.Column="1" Name="MemoryLimitSlider"
                                    Minimum="64" Maximum="2048" Value="512"
                                    TickFrequency="64" IsSnapToTickEnabled="True"
                                    VerticalAlignment="Center" Margin="0,0,10,0"
                                    ValueChanged="MemoryLimitSlider_ValueChanged"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Name="MemoryLimitLabel"
                                       Text="512" VerticalAlignment="Center" Width="40"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="Integration">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <GroupBox Grid.Row="0" Header="Windows Integration" Padding="10">
                        <StackPanel>
                            <CheckBox Name="ExplorerIntegrationCheckBox" Content="Integrate with Windows Explorer context menu" Margin="0,5"/>
                            <CheckBox Name="DefaultProgramCheckBox" Content="Set HaoZip as default archive program" Margin="0,5"/>
                            <CheckBox Name="StartupCheckBox" Content="Start HaoZip with Windows" Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>

                    <GroupBox Grid.Row="2" Header="File Associations" Padding="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="10"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="Associate HaoZip with the following file types:" Margin="0,0,0,5"/>
                            
                            <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                                <StackPanel Name="FileAssociationsPanel">
                                    <CheckBox Content=".zip - ZIP Archive" IsChecked="True" Margin="0,2"/>
                                    <CheckBox Content=".7z - 7-Zip Archive" IsChecked="True" Margin="0,2"/>
                                    <CheckBox Content=".rar - RAR Archive" IsChecked="True" Margin="0,2"/>
                                    <CheckBox Content=".tar - TAR Archive" IsChecked="True" Margin="0,2"/>
                                    <CheckBox Content=".gz - GZIP Archive" IsChecked="True" Margin="0,2"/>
                                    <CheckBox Content=".bz2 - BZIP2 Archive" IsChecked="True" Margin="0,2"/>
                                    <CheckBox Content=".cab - Cabinet Archive" IsChecked="False" Margin="0,2"/>
                                    <CheckBox Content=".iso - ISO Image" IsChecked="False" Margin="0,2"/>
                                </StackPanel>
                            </ScrollViewer>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="Advanced">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <GroupBox Grid.Row="0" Header="Logging" Padding="10">
                        <StackPanel>
                            <CheckBox Name="EnableLoggingCheckBox" Content="Enable operation logging" Margin="0,5"/>
                            <CheckBox Name="VerboseLoggingCheckBox" Content="Verbose logging (for debugging)" Margin="0,5"/>
                            <Grid Margin="0,10,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <Label Grid.Column="0" Content="Log Directory:" VerticalAlignment="Center"/>
                                <TextBox Grid.Column="1" Name="LogDirectoryTextBox" 
                                         Style="{StaticResource ModernTextBoxStyle}" 
                                         VerticalAlignment="Center" 
                                         Margin="10,0,10,0"/>
                                <Button Grid.Column="2" Content="Browse..." 
                                        Style="{StaticResource ModernButtonStyle}"
                                        Click="BrowseLogDirectory_Click"/>
                            </Grid>
                        </StackPanel>
                    </GroupBox>

                    <GroupBox Grid.Row="2" Header="Security" Padding="10">
                        <StackPanel>
                            <CheckBox Name="RememberPasswordsCheckBox" Content="Remember passwords for this session" Margin="0,5"/>
                            <CheckBox Name="WarnUnsafeOperationsCheckBox" Content="Warn about potentially unsafe operations" Margin="0,5" IsChecked="True"/>
                            <CheckBox Name="ScanExtractedFilesCheckBox" Content="Scan extracted files with Windows Defender" Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>
                </Grid>
            </TabItem>
        </TabControl>

        <Border Grid.Row="1" Background="#FFF0F0F0" BorderBrush="#FFCCCCCC" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="10">
                <Button Name="OkButton" Content="OK" Width="80" 
                        Style="{StaticResource ModernButtonStyle}" 
                        Click="OkButton_Click" Margin="0,0,10,0"/>
                <Button Name="CancelButton" Content="Cancel" Width="80" 
                        Style="{StaticResource ModernButtonStyle}" 
                        Click="CancelButton_Click" Margin="0,0,10,0"/>
                <Button Name="ApplyButton" Content="Apply" Width="80" 
                        Style="{StaticResource ModernButtonStyle}" 
                        Click="ApplyButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
