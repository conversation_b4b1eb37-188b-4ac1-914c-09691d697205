using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using HaoZip.Models;

namespace HaoZip.Services
{
    public interface ICompressionService
    {
        Task<bool> CompressAsync(CompressionRequest request, IProgress<ProgressInfo> progress, CancellationToken cancellationToken);
        Task<bool> ExtractAsync(ExtractionRequest request, IProgress<ProgressInfo> progress, CancellationToken cancellationToken);
        Task<List<ArchiveEntry>> ListArchiveContentsAsync(string archivePath, string password = null);
        bool IsArchiveFile(string filePath);
        List<string> GetSupportedFormats();
        bool TestArchive(string archivePath, string password = null);
    }

    public class CompressionRequest
    {
        public List<string> SourcePaths { get; set; } = new List<string>();
        public string DestinationPath { get; set; }
        public CompressionFormat Format { get; set; }
        public CompressionLevel Level { get; set; }
        public string Password { get; set; }
        public bool IncludeSubdirectories { get; set; } = true;
    }

    public class ExtractionRequest
    {
        public string ArchivePath { get; set; }
        public string DestinationPath { get; set; }
        public string Password { get; set; }
        public bool OverwriteExisting { get; set; } = false;
        public List<string> SelectedEntries { get; set; } = new List<string>();
    }

    public class ProgressInfo
    {
        public string CurrentFile { get; set; }
        public long ProcessedBytes { get; set; }
        public long TotalBytes { get; set; }
        public int ProcessedFiles { get; set; }
        public int TotalFiles { get; set; }
        public double PercentComplete => TotalBytes > 0 ? (double)ProcessedBytes / TotalBytes * 100 : 0;
        public string Status { get; set; }
    }
}
