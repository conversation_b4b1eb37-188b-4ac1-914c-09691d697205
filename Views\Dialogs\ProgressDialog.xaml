<Window x:Class="HaoZip.Views.Dialogs.ProgressDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🎮 HaoZip操作进行中 🎮"
        Height="280" Width="550"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False"
        Background="{StaticResource RetroBackground}"
        WindowStyle="SingleBorderWindow">

    <Border Background="{StaticResource RetroPanel}"
            BorderBrush="{StaticResource RetroHighlight}"
            BorderThickness="3"
            Margin="5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="15"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="15"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="15"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 复古游戏机风格状态标签 -->
            <Border Grid.Row="0"
                    Background="{StaticResource RetroAccent}"
                    BorderBrush="{StaticResource RetroBorder}"
                    BorderThickness="2"
                    Padding="10,5">
                <TextBlock Name="StatusLabel"
                           Text="准备操作中..."
                           FontFamily="Microsoft YaHei, SimHei"
                           FontWeight="Bold"
                           FontSize="14"
                           Foreground="{StaticResource RetroText}"
                           HorizontalAlignment="Center"/>
            </Border>

            <!-- 复古游戏机风格进度条 -->
            <ProgressBar Grid.Row="2" Name="MainProgressBar"
                         Height="24"
                         Style="{StaticResource RetroProgressBarStyle}"/>

            <!-- 百分比和速度显示 -->
            <Grid Grid.Row="4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="⚡ 进度:"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="12"
                               Foreground="{StaticResource RetroText}"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"/>
                    <TextBlock Name="PercentageLabel"
                               Text="0%"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="12"
                               Foreground="{StaticResource RetroHighlight}"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="🚀 速度:"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="12"
                               Foreground="{StaticResource RetroText}"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"/>
                    <TextBlock Name="SpeedLabel"
                               Text="0 B/S"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="12"
                               Foreground="{StaticResource RetroSecondary}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>

            <!-- 当前文件显示 -->
            <Border Grid.Row="6"
                    Background="{StaticResource RetroBackground}"
                    BorderBrush="{StaticResource RetroBorder}"
                    BorderThickness="1"
                    Padding="8,4">
                <TextBlock Name="CurrentFileLabel"
                           Text="等待文件中..."
                           FontFamily="Microsoft YaHei, SimHei"
                           FontWeight="Bold"
                           FontSize="11"
                           Foreground="{StaticResource RetroTextDark}"
                           TextTrimming="CharacterEllipsis"/>
            </Border>

            <!-- 底部信息和取消按钮 -->
            <Grid Grid.Row="8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📁" FontSize="14" Margin="0,0,5,0"/>
                    <TextBlock Name="FilesProgressLabel"
                               Text="文件: 0 / 0"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="11"
                               Foreground="{StaticResource RetroText}"
                               Margin="0,0,20,0"/>
                    <TextBlock Text="💾" FontSize="14" Margin="0,0,5,0"/>
                    <TextBlock Name="SizeProgressLabel"
                               Text="大小: 0 / 0"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="11"
                               Foreground="{StaticResource RetroText}"/>
                </StackPanel>

                <Button Grid.Column="1" Name="CancelButton"
                        Content="❌ 中止"
                        Width="100"
                        Style="{StaticResource RetroButtonStyle}"
                        Click="CancelButton_Click"/>
            </Grid>
        </Grid>
    </Border>
</Window>
