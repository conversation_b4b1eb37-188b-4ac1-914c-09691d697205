using System;
using System.Collections.Generic;

namespace HaoZip.Services
{
    public static class ServiceLocator
    {
        private static readonly Dictionary<Type, object> _services = new Dictionary<Type, object>();
        private static bool _isInitialized = false;

        public static void Initialize()
        {
            if (_isInitialized) return;

            RegisterService<ILoggingService>(new LoggingService());
            RegisterService<ICompressionService>(new CompressionService());
            RegisterService<ISettingsService>(new SettingsService());
            RegisterService<IFileSystemService>(new FileSystemService());
            RegisterService<IRegistryService>(new RegistryService());

            _isInitialized = true;
        }

        public static void RegisterService<T>(T service)
        {
            _services[typeof(T)] = service;
        }

        public static T GetService<T>()
        {
            if (_services.TryGetValue(typeof(T), out var service))
            {
                return (T)service;
            }
            throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered.");
        }

        public static bool IsRegistered<T>()
        {
            return _services.ContainsKey(typeof(T));
        }
    }
}
