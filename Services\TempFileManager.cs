using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

namespace HaoZip.Services
{
    /// <summary>
    /// 临时文件管理器，用于处理虚拟文件系统操作
    /// </summary>
    public class TempFileManager : IDisposable
    {
        private readonly string _tempDirectory;
        private readonly List<string> _tempFiles;
        private readonly List<string> _tempDirectories;
        private readonly ILoggingService _loggingService;

        public TempFileManager()
        {
            _tempFiles = new List<string>();
            _tempDirectories = new List<string>();
            _loggingService = ServiceLocator.GetService<ILoggingService>();
            
            // 创建专用的临时目录
            _tempDirectory = Path.Combine(Path.GetTempPath(), "HaoZip", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_tempDirectory);
            _tempDirectories.Add(_tempDirectory);
            
            _loggingService?.LogInfo($"创建临时目录: {_tempDirectory}");
        }

        /// <summary>
        /// 解压文件到临时目录
        /// </summary>
        public async Task<List<string>> ExtractFilesToTempAsync(
            ICompressionService compressionService,
            string archivePath,
            List<string> entryPaths,
            IProgress<ProgressInfo> progress = null)
        {
            var extractedFiles = new List<string>();
            
            try
            {
                _loggingService?.LogInfo($"开始解压 {entryPaths.Count} 个文件到临时目录");
                
                var request = new ExtractionRequest
                {
                    ArchivePath = archivePath,
                    DestinationPath = _tempDirectory,
                    SelectedEntries = entryPaths,
                    OverwriteExisting = true
                };

                var success = await compressionService.ExtractAsync(request, progress, System.Threading.CancellationToken.None);
                
                if (success)
                {
                    // 收集解压出的文件路径
                    foreach (var entryPath in entryPaths)
                    {
                        var fileName = Path.GetFileName(entryPath);
                        var tempFilePath = Path.Combine(_tempDirectory, fileName);
                        
                        if (File.Exists(tempFilePath))
                        {
                            extractedFiles.Add(tempFilePath);
                            _tempFiles.Add(tempFilePath);
                        }
                    }
                    
                    _loggingService?.LogInfo($"成功解压 {extractedFiles.Count} 个文件");
                }
                else
                {
                    _loggingService?.LogError("解压到临时目录失败");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("解压到临时目录时发生异常", ex);
                throw;
            }
            
            return extractedFiles;
        }

        /// <summary>
        /// 将文件复制到剪贴板
        /// </summary>
        public void CopyFilesToClipboard(List<string> filePaths)
        {
            try
            {
                if (filePaths == null || filePaths.Count == 0)
                    return;

                var fileList = new System.Collections.Specialized.StringCollection();
                foreach (var filePath in filePaths)
                {
                    if (File.Exists(filePath))
                    {
                        fileList.Add(filePath);
                    }
                }

                if (fileList.Count > 0)
                {
                    Clipboard.SetFileDropList(fileList);
                    _loggingService?.LogInfo($"已将 {fileList.Count} 个文件复制到剪贴板");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("复制文件到剪贴板失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 创建数据对象用于拖拽操作
        /// </summary>
        public DataObject CreateDataObject(List<string> filePaths)
        {
            try
            {
                var validFiles = filePaths.Where(File.Exists).ToArray();
                if (validFiles.Length == 0)
                    return null;

                var dataObject = new DataObject();
                dataObject.SetData(DataFormats.FileDrop, validFiles);
                
                _loggingService?.LogInfo($"创建拖拽数据对象，包含 {validFiles.Length} 个文件");
                return dataObject;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("创建拖拽数据对象失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取临时目录路径
        /// </summary>
        public string GetTempDirectory()
        {
            return _tempDirectory;
        }

        /// <summary>
        /// 清理指定的临时文件
        /// </summary>
        public void CleanupFiles(List<string> filePaths)
        {
            foreach (var filePath in filePaths)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                        _tempFiles.Remove(filePath);
                        _loggingService?.LogDebug($"删除临时文件: {filePath}");
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning($"无法删除临时文件 {filePath}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 清理所有临时文件和目录
        /// </summary>
        public void CleanupAll()
        {
            // 清理临时文件
            foreach (var tempFile in _tempFiles.ToList())
            {
                try
                {
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                        _loggingService?.LogDebug($"删除临时文件: {tempFile}");
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning($"无法删除临时文件 {tempFile}: {ex.Message}");
                }
            }
            _tempFiles.Clear();

            // 清理临时目录
            foreach (var tempDir in _tempDirectories.ToList())
            {
                try
                {
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                        _loggingService?.LogDebug($"删除临时目录: {tempDir}");
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning($"无法删除临时目录 {tempDir}: {ex.Message}");
                }
            }
            _tempDirectories.Clear();
        }

        public void Dispose()
        {
            CleanupAll();
        }
    }
}
