namespace HaoZip.Services
{
    public interface IRegistryService
    {
        void RegisterFileAssociation(string extension, string progId, string description, string iconPath, string commandPath);
        void UnregisterFileAssociation(string extension, string progId);
        void RegisterContextMenu(string fileType, string menuText, string commandPath);
        void UnregisterContextMenu(string fileType, string menuText);
        void SetAsDefaultProgram(string progId, string[] extensions);
        bool IsRegisteredAsDefault(string extension);
    }
}
