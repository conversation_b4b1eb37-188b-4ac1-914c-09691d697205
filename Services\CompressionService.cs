using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SharpCompress.Archives;
using SharpCompress.Archives.Zip;
using SharpCompress.Common;
using SharpCompress.Readers;
using SharpCompress.Writers;
using SharpCompress.Writers.Zip;
using HaoZip.Models;
using HaoZip.Utils;

namespace HaoZip.Services
{
    public class CompressionService : ICompressionService
    {
        private readonly Dictionary<string, CompressionFormat> _extensionMap = new Dictionary<string, CompressionFormat>
        {
            { ".zip", CompressionFormat.Zip },
            { ".tar", CompressionFormat.Tar },
            { ".gz", CompressionFormat.GZip },
            { ".bz2", CompressionFormat.BZip2 }
        };

        private readonly ILoggingService _loggingService;

        public CompressionService()
        {
            _loggingService = ServiceLocator.GetService<ILoggingService>();
        }

        public async Task<bool> CompressAsync(CompressionRequest request, IProgress<ProgressInfo> progress, CancellationToken cancellationToken)
        {
            using var monitor = new PerformanceMonitor("Compression", _loggingService);

            try
            {
                _loggingService?.LogInfo($"Starting compression to {request.DestinationPath}");

                var totalFiles = CountFiles(request.SourcePaths, request.IncludeSubdirectories);
                var processedFiles = 0;
                var totalBytes = CalculateTotalSize(request.SourcePaths, request.IncludeSubdirectories);
                var processedBytes = 0L;

                _loggingService?.LogInfo($"Compression stats: {totalFiles} files, {totalBytes} bytes");

                progress?.Report(new ProgressInfo
                {
                    Status = "Starting compression...",
                    TotalFiles = totalFiles,
                    TotalBytes = totalBytes
                });

                if (PerformanceHelper.IsLowMemory())
                {
                    _loggingService?.LogWarning("Low memory detected, forcing garbage collection");
                    PerformanceHelper.ForceGarbageCollection();
                }

                await Task.Run(() =>
                {
                    using var archive = CreateArchive(request.DestinationPath, request.Format, request.Level, request.Password);

                    foreach (var sourcePath in request.SourcePaths)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        if (File.Exists(sourcePath))
                        {
                            AddFileToArchive(archive, sourcePath, Path.GetFileName(sourcePath),
                                ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress);
                        }
                        else if (Directory.Exists(sourcePath))
                        {
                            AddDirectoryToArchive(archive, sourcePath, "", request.IncludeSubdirectories,
                                ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress, cancellationToken);
                        }

                        if (processedFiles % 100 == 0 && PerformanceHelper.IsLowMemory())
                        {
                            PerformanceHelper.ForceGarbageCollection();
                        }
                    }
                }, cancellationToken);

                progress?.Report(new ProgressInfo
                {
                    Status = "Compression completed successfully",
                    ProcessedFiles = totalFiles,
                    TotalFiles = totalFiles,
                    ProcessedBytes = totalBytes,
                    TotalBytes = totalBytes
                });

                _loggingService?.LogInfo($"Compression completed successfully in {monitor.ElapsedMilliseconds}ms");
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService?.LogInfo("Compression cancelled by user");
                progress?.Report(new ProgressInfo { Status = "Compression cancelled" });
                return false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Compression failed", ex);
                progress?.Report(new ProgressInfo { Status = $"Compression failed: {ex.Message}" });
                return false;
            }
        }

        public async Task<bool> ExtractAsync(ExtractionRequest request, IProgress<ProgressInfo> progress, CancellationToken cancellationToken)
        {
            using var monitor = new PerformanceMonitor("Extraction", _loggingService);

            try
            {
                _loggingService?.LogInfo($"开始解压: {request.ArchivePath} 到 {request.DestinationPath}");

                // 验证输入参数
                if (string.IsNullOrEmpty(request.ArchivePath) || !File.Exists(request.ArchivePath))
                {
                    var error = "压缩包文件不存在或路径无效";
                    _loggingService?.LogError(error);
                    progress?.Report(new ProgressInfo { Status = error });
                    return false;
                }

                if (string.IsNullOrEmpty(request.DestinationPath))
                {
                    var error = "目标路径不能为空";
                    _loggingService?.LogError(error);
                    progress?.Report(new ProgressInfo { Status = error });
                    return false;
                }

                // 确保目标目录存在
                try
                {
                    if (!Directory.Exists(request.DestinationPath))
                    {
                        Directory.CreateDirectory(request.DestinationPath);
                        _loggingService?.LogInfo($"创建目标目录: {request.DestinationPath}");
                    }
                }
                catch (Exception ex)
                {
                    var error = $"无法创建目标目录: {ex.Message}";
                    _loggingService?.LogError(error, ex);
                    progress?.Report(new ProgressInfo { Status = error });
                    return false;
                }

                progress?.Report(new ProgressInfo { Status = "正在分析压缩包..." });

                var totalFiles = 0;
                var totalBytes = 0L;

                await Task.Run(() =>
                {
                    try
                    {
                        _loggingService?.LogInfo("正在打开压缩包...");
                        using var archive = ArchiveFactory.Open(request.ArchivePath);
                        _loggingService?.LogInfo($"成功打开压缩包，条目数: {archive.Entries.Count()}");

                        var entries = archive.Entries.Where(e => !e.IsDirectory).ToList();
                        if (request.SelectedEntries.Any())
                        {
                            entries = entries.Where(e => request.SelectedEntries.Contains(e.Key ?? "")).ToList();
                            _loggingService?.LogInfo($"筛选后的条目数: {entries.Count}");
                        }

                        totalFiles = entries.Count;
                        totalBytes = entries.Sum(e => e.Size);
                        var processedFiles = 0;
                        var processedBytes = 0L;

                        _loggingService?.LogInfo($"开始解压 {totalFiles} 个文件，总大小: {totalBytes} 字节");

                        foreach (var entry in entries)
                        {
                            // 检查取消请求
                            if (cancellationToken.IsCancellationRequested)
                            {
                                _loggingService?.LogInfo("用户取消了解压操作");
                                cancellationToken.ThrowIfCancellationRequested();
                            }

                            var entryKey = entry.Key ?? "";
                            if (string.IsNullOrEmpty(entryKey))
                            {
                                _loggingService?.LogWarning("跳过空条目名");
                                continue;
                            }

                            progress?.Report(new ProgressInfo
                            {
                                CurrentFile = entryKey,
                                ProcessedFiles = processedFiles,
                                TotalFiles = totalFiles,
                                ProcessedBytes = processedBytes,
                                TotalBytes = totalBytes,
                                Status = $"正在解压 {entryKey}..."
                            });

                            try
                            {
                                _loggingService?.LogDebug($"解压文件: {entryKey}");

                                entry.WriteToDirectory(request.DestinationPath, new ExtractionOptions
                                {
                                    ExtractFullPath = true,
                                    Overwrite = request.OverwriteExisting
                                });

                                processedFiles++;
                                processedBytes += entry.Size;

                                _loggingService?.LogDebug($"成功解压: {entryKey}");
                            }
                            catch (Exception ex)
                            {
                                var errorMsg = $"解压文件 {entryKey} 失败: {ex.Message}";
                                _loggingService?.LogError(errorMsg, ex);

                                // 报告错误但继续处理其他文件
                                progress?.Report(new ProgressInfo
                                {
                                    CurrentFile = entryKey,
                                    ProcessedFiles = processedFiles,
                                    TotalFiles = totalFiles,
                                    ProcessedBytes = processedBytes,
                                    TotalBytes = totalBytes,
                                    Status = $"跳过文件 {entryKey}: {ex.Message}"
                                });

                                // 继续处理下一个文件
                                processedFiles++;
                            }
                        }

                        _loggingService?.LogInfo($"解压完成，处理了 {processedFiles} 个文件");
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError("解压过程中发生错误", ex);
                        throw;
                    }
                }, cancellationToken);

                progress?.Report(new ProgressInfo
                {
                    Status = "解压完成",
                    ProcessedFiles = totalFiles,
                    TotalFiles = totalFiles,
                    ProcessedBytes = totalBytes,
                    TotalBytes = totalBytes
                });

                _loggingService?.LogInfo($"解压操作成功完成，耗时: {monitor.ElapsedMilliseconds}ms");
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService?.LogInfo("解压操作被用户取消");
                progress?.Report(new ProgressInfo { Status = "解压已取消" });
                return false;
            }
            catch (Exception ex)
            {
                var errorMsg = $"解压失败: {ex.Message}";
                _loggingService?.LogError(errorMsg, ex);
                progress?.Report(new ProgressInfo { Status = errorMsg });
                return false;
            }
        }

        public async Task<List<ArchiveEntry>> ListArchiveContentsAsync(string archivePath, string password = null)
        {
            return await Task.Run(() =>
            {
                var entries = new List<ArchiveEntry>();
                
                try
                {
                    using var archive = ArchiveFactory.Open(archivePath);
                    
                    foreach (var entry in archive.Entries)
                    {
                        entries.Add(new ArchiveEntry
                        {
                            Name = Path.GetFileName(entry.Key),
                            FullPath = entry.Key,
                            Size = entry.Size,
                            CompressedSize = entry.CompressedSize,
                            ModifiedTime = entry.LastModifiedTime ?? DateTime.MinValue,
                            IsDirectory = entry.IsDirectory,
                            Type = entry.IsDirectory ? "Folder" : GetFileType(entry.Key)
                        });
                    }
                }
                catch (Exception)
                {
                    throw;
                }

                return entries;
            });
        }

        public bool IsArchiveFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return _extensionMap.ContainsKey(extension);
        }

        public List<string> GetSupportedFormats()
        {
            return _extensionMap.Keys.ToList();
        }

        public bool TestArchive(string archivePath, string password = null)
        {
            try
            {
                using var archive = ArchiveFactory.Open(archivePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private IWriter CreateArchive(string destinationPath, CompressionFormat format, CompressionLevel level, string password)
        {
            var compressionType = GetCompressionType(level);

            return format switch
            {
                CompressionFormat.Zip => new ZipWriter(File.Create(destinationPath), new ZipWriterOptions(compressionType)),
                _ => throw new NotSupportedException($"Format {format} is not supported for compression")
            };
        }

        private CompressionType GetCompressionType(CompressionLevel level)
        {
            return level switch
            {
                CompressionLevel.Store => CompressionType.None,
                CompressionLevel.Fastest => CompressionType.Deflate,
                CompressionLevel.Fast => CompressionType.Deflate,
                CompressionLevel.Normal => CompressionType.Deflate,
                CompressionLevel.Maximum => CompressionType.Deflate,
                CompressionLevel.Ultra => CompressionType.Deflate,
                _ => CompressionType.Deflate
            };
        }

        private void AddFileToArchive(IWriter archive, string filePath, string entryName, 
            ref int processedFiles, ref long processedBytes, int totalFiles, long totalBytes, IProgress<ProgressInfo> progress)
        {
            var fileInfo = new FileInfo(filePath);
            
            progress?.Report(new ProgressInfo
            {
                CurrentFile = entryName,
                ProcessedFiles = processedFiles,
                TotalFiles = totalFiles,
                ProcessedBytes = processedBytes,
                TotalBytes = totalBytes,
                Status = $"Compressing {entryName}..."
            });

            archive.Write(entryName, filePath);
            
            processedFiles++;
            processedBytes += fileInfo.Length;
        }

        private void AddDirectoryToArchive(IWriter archive, string directoryPath, string relativePath, bool includeSubdirectories,
            ref int processedFiles, ref long processedBytes, int totalFiles, long totalBytes, IProgress<ProgressInfo> progress, CancellationToken cancellationToken)
        {
            var files = Directory.GetFiles(directoryPath);
            foreach (var file in files)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var fileName = Path.GetFileName(file);
                var entryName = string.IsNullOrEmpty(relativePath) ? fileName : Path.Combine(relativePath, fileName);
                
                AddFileToArchive(archive, file, entryName, ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress);
            }

            if (includeSubdirectories)
            {
                var directories = Directory.GetDirectories(directoryPath);
                foreach (var directory in directories)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var dirName = Path.GetFileName(directory);
                    var newRelativePath = string.IsNullOrEmpty(relativePath) ? dirName : Path.Combine(relativePath, dirName);
                    
                    AddDirectoryToArchive(archive, directory, newRelativePath, includeSubdirectories,
                        ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress, cancellationToken);
                }
            }
        }

        private int CountFiles(List<string> sourcePaths, bool includeSubdirectories)
        {
            var count = 0;
            foreach (var path in sourcePaths)
            {
                if (File.Exists(path))
                {
                    count++;
                }
                else if (Directory.Exists(path))
                {
                    count += CountFilesInDirectory(path, includeSubdirectories);
                }
            }
            return count;
        }

        private int CountFilesInDirectory(string directoryPath, bool includeSubdirectories)
        {
            var count = Directory.GetFiles(directoryPath).Length;
            if (includeSubdirectories)
            {
                foreach (var subDir in Directory.GetDirectories(directoryPath))
                {
                    count += CountFilesInDirectory(subDir, includeSubdirectories);
                }
            }
            return count;
        }

        private long CalculateTotalSize(List<string> sourcePaths, bool includeSubdirectories)
        {
            long totalSize = 0;
            foreach (var path in sourcePaths)
            {
                if (File.Exists(path))
                {
                    totalSize += new FileInfo(path).Length;
                }
                else if (Directory.Exists(path))
                {
                    totalSize += CalculateDirectorySize(path, includeSubdirectories);
                }
            }
            return totalSize;
        }

        private long CalculateDirectorySize(string directoryPath, bool includeSubdirectories)
        {
            long size = 0;
            foreach (var file in Directory.GetFiles(directoryPath))
            {
                size += new FileInfo(file).Length;
            }
            
            if (includeSubdirectories)
            {
                foreach (var subDir in Directory.GetDirectories(directoryPath))
                {
                    size += CalculateDirectorySize(subDir, includeSubdirectories);
                }
            }
            
            return size;
        }

        private string GetFileType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".txt" => "Text Document",
                ".pdf" => "PDF Document",
                ".doc" or ".docx" => "Word Document",
                ".xls" or ".xlsx" => "Excel Spreadsheet",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "Image",
                ".mp3" or ".wav" or ".flac" => "Audio",
                ".mp4" or ".avi" or ".mkv" or ".mov" => "Video",
                ".exe" => "Application",
                ".dll" => "Library",
                _ => "File"
            };
        }
    }
}
