using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SharpCompress.Archives;
using SharpCompress.Archives.Zip;
using SharpCompress.Common;
using SharpCompress.Readers;
using SharpCompress.Writers;
using SharpCompress.Writers.Zip;
using HaoZip.Models;
using HaoZip.Utils;

namespace HaoZip.Services
{
    public class CompressionService : ICompressionService
    {
        private readonly Dictionary<string, CompressionFormat> _extensionMap = new Dictionary<string, CompressionFormat>
        {
            { ".zip", CompressionFormat.Zip },
            { ".tar", CompressionFormat.Tar },
            { ".gz", CompressionFormat.GZip },
            { ".bz2", CompressionFormat.BZip2 }
        };

        private readonly ILoggingService _loggingService;

        public CompressionService()
        {
            _loggingService = ServiceLocator.GetService<ILoggingService>();
        }

        public async Task<bool> CompressAsync(CompressionRequest request, IProgress<ProgressInfo> progress, CancellationToken cancellationToken)
        {
            using var monitor = new PerformanceMonitor("Compression", _loggingService);

            try
            {
                _loggingService?.LogInfo($"Starting compression to {request.DestinationPath}");

                var totalFiles = CountFiles(request.SourcePaths, request.IncludeSubdirectories);
                var processedFiles = 0;
                var totalBytes = CalculateTotalSize(request.SourcePaths, request.IncludeSubdirectories);
                var processedBytes = 0L;

                _loggingService?.LogInfo($"Compression stats: {totalFiles} files, {totalBytes} bytes");

                progress?.Report(new ProgressInfo
                {
                    Status = "Starting compression...",
                    TotalFiles = totalFiles,
                    TotalBytes = totalBytes
                });

                if (PerformanceHelper.IsLowMemory())
                {
                    _loggingService?.LogWarning("Low memory detected, forcing garbage collection");
                    PerformanceHelper.ForceGarbageCollection();
                }

                await Task.Run(() =>
                {
                    using var archive = CreateArchive(request.DestinationPath, request.Format, request.Level, request.Password);

                    foreach (var sourcePath in request.SourcePaths)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        if (File.Exists(sourcePath))
                        {
                            AddFileToArchive(archive, sourcePath, Path.GetFileName(sourcePath),
                                ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress);
                        }
                        else if (Directory.Exists(sourcePath))
                        {
                            AddDirectoryToArchive(archive, sourcePath, "", request.IncludeSubdirectories,
                                ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress, cancellationToken);
                        }

                        if (processedFiles % 100 == 0 && PerformanceHelper.IsLowMemory())
                        {
                            PerformanceHelper.ForceGarbageCollection();
                        }
                    }
                }, cancellationToken);

                progress?.Report(new ProgressInfo
                {
                    Status = "Compression completed successfully",
                    ProcessedFiles = totalFiles,
                    TotalFiles = totalFiles,
                    ProcessedBytes = totalBytes,
                    TotalBytes = totalBytes
                });

                _loggingService?.LogInfo($"Compression completed successfully in {monitor.ElapsedMilliseconds}ms");
                return true;
            }
            catch (OperationCanceledException)
            {
                _loggingService?.LogInfo("Compression cancelled by user");
                progress?.Report(new ProgressInfo { Status = "Compression cancelled" });
                return false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError("Compression failed", ex);
                progress?.Report(new ProgressInfo { Status = $"Compression failed: {ex.Message}" });
                return false;
            }
        }

        public async Task<bool> ExtractAsync(ExtractionRequest request, IProgress<ProgressInfo> progress, CancellationToken cancellationToken)
        {
            try
            {
                progress?.Report(new ProgressInfo { Status = "Starting extraction..." });

                var totalFiles = 0;
                var totalBytes = 0L;

                await Task.Run(() =>
                {
                    using var archive = ArchiveFactory.Open(request.ArchivePath);

                    var entries = archive.Entries.Where(e => !e.IsDirectory).ToList();
                    if (request.SelectedEntries.Any())
                    {
                        entries = entries.Where(e => request.SelectedEntries.Contains(e.Key ?? "")).ToList();
                    }

                    totalFiles = entries.Count;
                    totalBytes = entries.Sum(e => e.Size);
                    var processedFiles = 0;
                    var processedBytes = 0L;

                    foreach (var entry in entries)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var entryKey = entry.Key ?? "";
                        if (string.IsNullOrEmpty(entryKey)) continue;

                        progress?.Report(new ProgressInfo
                        {
                            CurrentFile = entryKey,
                            ProcessedFiles = processedFiles,
                            TotalFiles = totalFiles,
                            ProcessedBytes = processedBytes,
                            TotalBytes = totalBytes,
                            Status = $"正在解压 {entryKey}..."
                        });

                        try
                        {
                            entry.WriteToDirectory(request.DestinationPath, new ExtractionOptions
                            {
                                ExtractFullPath = true,
                                Overwrite = request.OverwriteExisting
                            });

                            processedFiles++;
                            processedBytes += entry.Size;
                        }
                        catch (Exception ex)
                        {
                            // 记录错误但继续处理其他文件
                            progress?.Report(new ProgressInfo
                            {
                                Status = $"跳过文件 {entryKey}: {ex.Message}"
                            });
                        }
                    }
                }, cancellationToken);

                progress?.Report(new ProgressInfo
                {
                    Status = "解压完成",
                    ProcessedFiles = totalFiles,
                    TotalFiles = totalFiles,
                    ProcessedBytes = totalBytes,
                    TotalBytes = totalBytes
                });
                return true;
            }
            catch (OperationCanceledException)
            {
                progress?.Report(new ProgressInfo { Status = "解压已取消" });
                return false;
            }
            catch (Exception ex)
            {
                progress?.Report(new ProgressInfo { Status = $"解压失败: {ex.Message}" });
                return false;
            }
        }

        public async Task<List<ArchiveEntry>> ListArchiveContentsAsync(string archivePath, string password = null)
        {
            return await Task.Run(() =>
            {
                var entries = new List<ArchiveEntry>();
                
                try
                {
                    using var archive = ArchiveFactory.Open(archivePath);
                    
                    foreach (var entry in archive.Entries)
                    {
                        entries.Add(new ArchiveEntry
                        {
                            Name = Path.GetFileName(entry.Key),
                            FullPath = entry.Key,
                            Size = entry.Size,
                            CompressedSize = entry.CompressedSize,
                            ModifiedTime = entry.LastModifiedTime ?? DateTime.MinValue,
                            IsDirectory = entry.IsDirectory,
                            Type = entry.IsDirectory ? "Folder" : GetFileType(entry.Key)
                        });
                    }
                }
                catch (Exception)
                {
                    throw;
                }

                return entries;
            });
        }

        public bool IsArchiveFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return _extensionMap.ContainsKey(extension);
        }

        public List<string> GetSupportedFormats()
        {
            return _extensionMap.Keys.ToList();
        }

        public bool TestArchive(string archivePath, string password = null)
        {
            try
            {
                using var archive = ArchiveFactory.Open(archivePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private IWriter CreateArchive(string destinationPath, CompressionFormat format, CompressionLevel level, string password)
        {
            var compressionType = GetCompressionType(level);

            return format switch
            {
                CompressionFormat.Zip => new ZipWriter(File.Create(destinationPath), new ZipWriterOptions(compressionType)),
                _ => throw new NotSupportedException($"Format {format} is not supported for compression")
            };
        }

        private CompressionType GetCompressionType(CompressionLevel level)
        {
            return level switch
            {
                CompressionLevel.Store => CompressionType.None,
                CompressionLevel.Fastest => CompressionType.Deflate,
                CompressionLevel.Fast => CompressionType.Deflate,
                CompressionLevel.Normal => CompressionType.Deflate,
                CompressionLevel.Maximum => CompressionType.Deflate,
                CompressionLevel.Ultra => CompressionType.Deflate,
                _ => CompressionType.Deflate
            };
        }

        private void AddFileToArchive(IWriter archive, string filePath, string entryName, 
            ref int processedFiles, ref long processedBytes, int totalFiles, long totalBytes, IProgress<ProgressInfo> progress)
        {
            var fileInfo = new FileInfo(filePath);
            
            progress?.Report(new ProgressInfo
            {
                CurrentFile = entryName,
                ProcessedFiles = processedFiles,
                TotalFiles = totalFiles,
                ProcessedBytes = processedBytes,
                TotalBytes = totalBytes,
                Status = $"Compressing {entryName}..."
            });

            archive.Write(entryName, filePath);
            
            processedFiles++;
            processedBytes += fileInfo.Length;
        }

        private void AddDirectoryToArchive(IWriter archive, string directoryPath, string relativePath, bool includeSubdirectories,
            ref int processedFiles, ref long processedBytes, int totalFiles, long totalBytes, IProgress<ProgressInfo> progress, CancellationToken cancellationToken)
        {
            var files = Directory.GetFiles(directoryPath);
            foreach (var file in files)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var fileName = Path.GetFileName(file);
                var entryName = string.IsNullOrEmpty(relativePath) ? fileName : Path.Combine(relativePath, fileName);
                
                AddFileToArchive(archive, file, entryName, ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress);
            }

            if (includeSubdirectories)
            {
                var directories = Directory.GetDirectories(directoryPath);
                foreach (var directory in directories)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var dirName = Path.GetFileName(directory);
                    var newRelativePath = string.IsNullOrEmpty(relativePath) ? dirName : Path.Combine(relativePath, dirName);
                    
                    AddDirectoryToArchive(archive, directory, newRelativePath, includeSubdirectories,
                        ref processedFiles, ref processedBytes, totalFiles, totalBytes, progress, cancellationToken);
                }
            }
        }

        private int CountFiles(List<string> sourcePaths, bool includeSubdirectories)
        {
            var count = 0;
            foreach (var path in sourcePaths)
            {
                if (File.Exists(path))
                {
                    count++;
                }
                else if (Directory.Exists(path))
                {
                    count += CountFilesInDirectory(path, includeSubdirectories);
                }
            }
            return count;
        }

        private int CountFilesInDirectory(string directoryPath, bool includeSubdirectories)
        {
            var count = Directory.GetFiles(directoryPath).Length;
            if (includeSubdirectories)
            {
                foreach (var subDir in Directory.GetDirectories(directoryPath))
                {
                    count += CountFilesInDirectory(subDir, includeSubdirectories);
                }
            }
            return count;
        }

        private long CalculateTotalSize(List<string> sourcePaths, bool includeSubdirectories)
        {
            long totalSize = 0;
            foreach (var path in sourcePaths)
            {
                if (File.Exists(path))
                {
                    totalSize += new FileInfo(path).Length;
                }
                else if (Directory.Exists(path))
                {
                    totalSize += CalculateDirectorySize(path, includeSubdirectories);
                }
            }
            return totalSize;
        }

        private long CalculateDirectorySize(string directoryPath, bool includeSubdirectories)
        {
            long size = 0;
            foreach (var file in Directory.GetFiles(directoryPath))
            {
                size += new FileInfo(file).Length;
            }
            
            if (includeSubdirectories)
            {
                foreach (var subDir in Directory.GetDirectories(directoryPath))
                {
                    size += CalculateDirectorySize(subDir, includeSubdirectories);
                }
            }
            
            return size;
        }

        private string GetFileType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".txt" => "Text Document",
                ".pdf" => "PDF Document",
                ".doc" or ".docx" => "Word Document",
                ".xls" or ".xlsx" => "Excel Spreadsheet",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "Image",
                ".mp3" or ".wav" or ".flac" => "Audio",
                ".mp4" or ".avi" or ".mkv" or ".mov" => "Video",
                ".exe" => "Application",
                ".dll" => "Library",
                _ => "File"
            };
        }
    }
}
