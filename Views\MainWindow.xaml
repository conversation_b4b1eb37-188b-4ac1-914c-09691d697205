<Window x:Class="HaoZip.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🎮 HaoZip复古街机 🎮"
        Height="600" Width="900"
        MinHeight="400" MinWidth="600"
        Icon="../Resources/haozip.ico"
        AllowDrop="True"
        Drop="MainWindow_Drop"
        DragOver="MainWindow_DragOver"
        Background="{StaticResource RetroBackground}"
        WindowStyle="SingleBorderWindow">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>  <!-- 标题栏 -->
            <RowDefinition Height="Auto"/>  <!-- 菜单 -->
            <RowDefinition Height="Auto"/>  <!-- 工具栏 -->
            <RowDefinition Height="*"/>     <!-- 主内容 -->
            <RowDefinition Height="Auto"/>  <!-- 状态栏 -->
        </Grid.RowDefinitions>

        <!-- 复古游戏机风格标题栏 -->
        <Border Grid.Row="0"
                Background="{StaticResource RetroPanel}"
                BorderThickness="0,0,0,3"
                BorderBrush="{StaticResource RetroHighlight}">
            <Grid Height="40">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧装饰 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock Text="🕹️" FontSize="20" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="HaoZip"
                               FontFamily="Microsoft YaHei, SimHei, 'Courier New', monospace"
                               FontWeight="Bold"
                               FontSize="16"
                               Foreground="{StaticResource RetroHighlight}"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 中央标题 -->
                <TextBlock Grid.Column="1"
                           Text="大厅"
                           FontFamily="Microsoft YaHei, SimHei, 'Courier New', monospace"
                           FontWeight="Bold"
                           FontSize="14"
                           Foreground="{StaticResource RetroText}"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>

                <!-- 右侧装饰 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock Text="就绪"
                               FontFamily="Microsoft YaHei, SimHei, 'Courier New', monospace"
                               FontWeight="Bold"
                               FontSize="12"
                               Foreground="{StaticResource RetroSuccess}"
                               VerticalAlignment="Center"
                               Margin="0,0,5,0"/>
                    <TextBlock Text="🎯" FontSize="16" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <Menu Grid.Row="1" Style="{StaticResource RetroMenuStyle}">
            <MenuItem Header="📁 文件" Style="{StaticResource RetroMenuItemStyle}">
                <MenuItem Header="🆕 新建压缩包..." Click="NewArchive_Click" InputGestureText="Ctrl+N" Style="{StaticResource RetroMenuItemStyle}"/>
                <MenuItem Header="📂 打开压缩包..." Click="OpenArchive_Click" InputGestureText="Ctrl+O" Style="{StaticResource RetroMenuItemStyle}"/>
                <Separator Background="{StaticResource RetroBorder}"/>
                <MenuItem Header="➕ 添加文件..." Click="AddFiles_Click" InputGestureText="Ctrl+A" Style="{StaticResource RetroMenuItemStyle}"/>
                <MenuItem Header="📁 添加文件夹..." Click="AddFolder_Click" Style="{StaticResource RetroMenuItemStyle}"/>
                <Separator Background="{StaticResource RetroBorder}"/>
                <MenuItem Header="📤 解压全部..." Click="ExtractAll_Click" InputGestureText="Ctrl+E" Style="{StaticResource RetroMenuItemStyle}"/>
                <MenuItem Header="📋 解压选中..." Click="ExtractSelected_Click" Style="{StaticResource RetroMenuItemStyle}"/>
                <Separator Background="{StaticResource RetroBorder}"/>
                <MenuItem Header="🔍 测试压缩包" Click="TestArchive_Click" InputGestureText="Ctrl+T" Style="{StaticResource RetroMenuItemStyle}"/>
                <Separator Background="{StaticResource RetroBorder}"/>
                <MenuItem Header="❌ 退出" Click="Exit_Click" InputGestureText="Alt+F4" Style="{StaticResource RetroMenuItemStyle}"/>
            </MenuItem>
            <MenuItem Header="✏️ 编辑" Style="{StaticResource RetroMenuItemStyle}">
                <MenuItem Header="🔘 全选" Click="SelectAll_Click" InputGestureText="Ctrl+A" Style="{StaticResource RetroMenuItemStyle}"/>
                <MenuItem Header="🔄 反选" Click="InvertSelection_Click" Style="{StaticResource RetroMenuItemStyle}"/>
                <MenuItem Header="🗑️ 删除选中" Click="DeleteSelected_Click" InputGestureText="Del" Style="{StaticResource RetroMenuItemStyle}"/>
            </MenuItem>
            <MenuItem Header="🔧 工具" Style="{StaticResource RetroMenuItemStyle}">
                <MenuItem Header="⚙️ 设置..." Click="Settings_Click" InputGestureText="Ctrl+," Style="{StaticResource RetroMenuItemStyle}"/>
                <MenuItem Header="🔗 系统集成..." Click="SystemIntegration_Click" Style="{StaticResource RetroMenuItemStyle}"/>
            </MenuItem>
            <MenuItem Header="❓ 帮助" Style="{StaticResource RetroMenuItemStyle}">
                <MenuItem Header="ℹ️ 关于HaoZip..." Click="About_Click" InputGestureText="F1" Style="{StaticResource RetroMenuItemStyle}"/>
            </MenuItem>
        </Menu>

        <!-- 复古游戏机风格工具栏 -->
        <Border Grid.Row="2"
                Background="{StaticResource RetroAccent}"
                BorderThickness="0,0,0,2"
                BorderBrush="{StaticResource RetroHighlight}">
            <StackPanel Orientation="Horizontal" Margin="10,8">
                <Button Name="NewButton"
                        Click="NewArchive_Click"
                        ToolTip="新建压缩包 (CTRL+N)"
                        Style="{StaticResource RetroButtonStyle}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🆕" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="新建" FontFamily="Microsoft YaHei, SimHei" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <Button Name="OpenButton"
                        Click="OpenArchive_Click"
                        ToolTip="打开压缩包 (CTRL+O)"
                        Style="{StaticResource RetroButtonStyle}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📂" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="打开" FontFamily="Microsoft YaHei, SimHei" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <!-- 分隔符 -->
                <Rectangle Width="2"
                           Height="30"
                           Fill="{StaticResource RetroBorder}"
                           Margin="8,0"/>

                <Button Name="AddButton"
                        Click="AddFiles_Click"
                        ToolTip="添加文件 (CTRL+A)"
                        Style="{StaticResource RetroButtonStyle}"
                        Margin="8,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="添加" FontFamily="Microsoft YaHei, SimHei" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <Button Name="ExtractButton"
                        Click="ExtractAll_Click"
                        ToolTip="解压全部 (CTRL+E)"
                        Style="{StaticResource RetroButtonStyle}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📤" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="解压" FontFamily="Microsoft YaHei, SimHei" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <!-- 分隔符 -->
                <Rectangle Width="2"
                           Height="30"
                           Fill="{StaticResource RetroBorder}"
                           Margin="8,0"/>

                <Button Name="TestButton"
                        Click="TestArchive_Click"
                        ToolTip="测试压缩包 (CTRL+T)"
                        Style="{StaticResource RetroButtonStyle}"
                        Margin="8,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔍" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="测试" FontFamily="Microsoft YaHei, SimHei" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <!-- 分隔符 -->
                <Rectangle Width="2"
                           Height="30"
                           Fill="{StaticResource RetroBorder}"
                           Margin="8,0"/>

                <!-- 压缩级别选择 -->
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="8,0">
                    <TextBlock Text="⚡ 级别:"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="10"
                               Foreground="{StaticResource RetroText}"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"/>
                    <ComboBox Name="CompressionLevelCombo"
                              Width="140"
                              SelectedIndex="3"
                              ToolTip="压缩级别"
                              Background="{StaticResource RetroBackground}"
                              Foreground="{StaticResource RetroText}"
                              BorderBrush="{StaticResource RetroBorder}"
                              FontFamily="Microsoft YaHei, SimHei"
                              FontWeight="Bold"
                              FontSize="10">
                        <ComboBoxItem Content="🔸 存储" Foreground="{StaticResource RetroText}"/>
                        <ComboBoxItem Content="🔸 最快" Foreground="{StaticResource RetroText}"/>
                        <ComboBoxItem Content="🔸 快速" Foreground="{StaticResource RetroText}"/>
                        <ComboBoxItem Content="🔸 标准" Foreground="{StaticResource RetroText}"/>
                        <ComboBoxItem Content="🔸 最大" Foreground="{StaticResource RetroText}"/>
                        <ComboBoxItem Content="🔸 极限" Foreground="{StaticResource RetroText}"/>
                    </ComboBox>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主内容区域 - 双面板布局 -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*" MinWidth="400"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧面板 - 文件夹树形结构 -->
            <Border Grid.Column="0"
                    Background="{StaticResource RetroBackground}"
                    BorderBrush="{StaticResource RetroBorder}"
                    BorderThickness="2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 左侧面板标题 -->
                    <Border Grid.Row="0"
                            Background="{StaticResource RetroAccent}"
                            BorderBrush="{StaticResource RetroBorder}"
                            BorderThickness="0,0,0,2"
                            Padding="10,5">
                        <TextBlock Text="📁 文件夹结构"
                                   FontFamily="Microsoft YaHei, SimHei"
                                   FontWeight="Bold"
                                   FontSize="12"
                                   Foreground="{StaticResource RetroText}"/>
                    </Border>

                    <!-- 文件夹树形视图 -->
                    <TreeView Grid.Row="1"
                              Name="FolderTreeView"
                              Background="{StaticResource RetroBackground}"
                              Foreground="{StaticResource RetroText}"
                              BorderThickness="0"
                              Padding="5"
                              SelectedItemChanged="FolderTreeView_SelectedItemChanged">
                        <TreeView.ItemContainerStyle>
                            <Style TargetType="TreeViewItem">
                                <Setter Property="Foreground" Value="{StaticResource RetroText}"/>
                                <Setter Property="FontFamily" Value="Microsoft YaHei, SimHei"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontSize" Value="11"/>
                                <Setter Property="Padding" Value="2"/>
                                <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{StaticResource RetroHighlight}"/>
                                        <Setter Property="Foreground" Value="{StaticResource RetroBackground}"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TreeView.ItemContainerStyle>
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <TextBlock Text="{Binding DisplayName}"
                                           FontFamily="Microsoft YaHei, SimHei"
                                           FontWeight="Bold"
                                           FontSize="11"/>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </Grid>
            </Border>

            <!-- 分隔条 -->
            <GridSplitter Grid.Column="1"
                          Background="{StaticResource RetroBorder}"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch"
                          ResizeBehavior="PreviousAndNext"/>

            <!-- 右侧面板 - 文件列表 -->
            <Border Grid.Column="2"
                    Background="{StaticResource RetroBackground}"
                    BorderBrush="{StaticResource RetroBorder}"
                    BorderThickness="2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 右侧面板标题和路径 -->
                    <Border Grid.Row="0"
                            Background="{StaticResource RetroAccent}"
                            BorderBrush="{StaticResource RetroBorder}"
                            BorderThickness="0,0,0,2"
                            Padding="10,5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0"
                                       Text="📄 文件列表 - "
                                       FontFamily="Microsoft YaHei, SimHei"
                                       FontWeight="Bold"
                                       FontSize="12"
                                       Foreground="{StaticResource RetroText}"/>
                            <TextBlock Grid.Column="1"
                                       Name="CurrentPathLabel"
                                       Text="/"
                                       FontFamily="Microsoft YaHei, SimHei"
                                       FontWeight="Bold"
                                       FontSize="11"
                                       Foreground="{StaticResource RetroSecondary}"/>
                        </Grid>
                    </Border>

                    <!-- 复古游戏机风格数据网格 -->
                    <DataGrid Grid.Row="1"
                              Name="FileListGrid"
                              Style="{StaticResource RetroDataGridStyle}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              SelectionMode="Extended"
                              SelectionChanged="FileListGrid_SelectionChanged"
                              MouseDoubleClick="FileListGrid_MouseDoubleClick"
                              KeyDown="FileListGrid_KeyDown"
                              MouseMove="FileListGrid_MouseMove"
                              PreviewMouseLeftButtonDown="FileListGrid_PreviewMouseLeftButtonDown">
                <DataGrid.Columns>
                    <DataGridCheckBoxColumn Header="✓"
                                          Binding="{Binding IsSelected}"
                                          Width="40"
                                          HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}"/>
                    <DataGridTextColumn Header="📄 名称"
                                      Binding="{Binding Name}"
                                      Width="*"
                                      MinWidth="200"
                                      HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}"/>
                    <DataGridTextColumn Header="🏷️ 类型"
                                      Binding="{Binding Type}"
                                      Width="120"
                                      HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}"/>
                    <DataGridTextColumn Header="📏 大小"
                                      Binding="{Binding SizeFormatted}"
                                      Width="100"
                                      HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="{StaticResource RetroSecondary}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="🗜️ 压缩后"
                                      Binding="{Binding CompressedSizeFormatted}"
                                      Width="120"
                                      HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="{StaticResource RetroSuccess}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="📊 比率"
                                      Width="80"
                                      HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}">
                        <DataGridTextColumn.Binding>
                            <Binding Path="CompressionRatio" StringFormat="{}{0:F1}%"/>
                        </DataGridTextColumn.Binding>
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="{StaticResource RetroHighlight}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="🕒 修改时间"
                                      Width="160"
                                      HeaderStyle="{StaticResource RetroDataGridColumnHeaderStyle}">
                        <DataGridTextColumn.Binding>
                            <Binding Path="ModifiedTime" StringFormat="{}{0:yyyy-MM-dd HH:mm:ss}"/>
                        </DataGridTextColumn.Binding>
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="{StaticResource RetroTextDark}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    </DataGrid.Columns>
                    </DataGrid>

                    <!-- 复古游戏机风格空状态提示 -->
                    <Border Grid.Row="1"
                            Name="EmptyStatePanel"
                            Background="{StaticResource RetroAccent}"
                            BorderBrush="{StaticResource RetroHighlight}"
                            BorderThickness="3"
                            CornerRadius="0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Padding="40,20"
                            Visibility="Visible">
                        <Border.Effect>
                            <DropShadowEffect Color="#000000" BlurRadius="10" ShadowDepth="5" Opacity="0.5"/>
                        </Border.Effect>
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="🎮 Game Over 🎮"
                                       FontFamily="Microsoft YaHei, SimHei"
                                       FontWeight="Bold"
                                       FontSize="20"
                                       Foreground="{StaticResource RetroHighlight}"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,10"/>
                            <TextBlock Text="请插入压缩包继续游戏"
                                       FontFamily="Microsoft YaHei, SimHei"
                                       FontWeight="Bold"
                                       FontSize="14"
                                       Foreground="{StaticResource RetroText}"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,5"/>
                            <TextBlock Text="拖拽文件到此处或使用菜单"
                                       FontFamily="Microsoft YaHei, SimHei"
                                       FontWeight="Bold"
                                       FontSize="12"
                                       Foreground="{StaticResource RetroTextDark}"
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- 复古游戏机风格状态栏 -->
        <StatusBar Grid.Row="4" Style="{StaticResource RetroStatusBarStyle}">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🎯 状态:"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="10"
                               Margin="0,0,5,0"/>
                    <TextBlock Name="StatusText"
                               Text="就绪"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="10"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📁" FontSize="12" Margin="0,0,3,0"/>
                    <TextBlock Name="FileCountText"
                               Text="0 个文件"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="10"/>
                    <Rectangle Width="1"
                               Height="16"
                               Fill="{StaticResource RetroBorder}"
                               Margin="10,0"/>
                    <TextBlock Text="💾" FontSize="12" Margin="0,0,3,0"/>
                    <TextBlock Name="TotalSizeText"
                               Text="0 字节"
                               FontFamily="Microsoft YaHei, SimHei"
                               FontWeight="Bold"
                               FontSize="10"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
