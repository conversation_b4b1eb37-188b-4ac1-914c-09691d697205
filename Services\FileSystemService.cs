using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace HaoZip.Services
{
    public class FileSystemService : IFileSystemService
    {
        public bool FileExists(string path)
        {
            return File.Exists(path);
        }

        public bool DirectoryExists(string path)
        {
            return Directory.Exists(path);
        }

        public long GetFileSize(string path)
        {
            return new FileInfo(path).Length;
        }

        public string GetFileName(string path)
        {
            return Path.GetFileName(path);
        }

        public string GetDirectoryName(string path)
        {
            return Path.GetDirectoryName(path);
        }

        public string GetFileExtension(string path)
        {
            return Path.GetExtension(path);
        }

        public List<string> GetFiles(string directory, string searchPattern = "*", bool includeSubdirectories = false)
        {
            var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            return Directory.GetFiles(directory, searchPattern, searchOption).ToList();
        }

        public List<string> GetDirectories(string directory)
        {
            return Directory.GetDirectories(directory).ToList();
        }

        public void CreateDirectory(string path)
        {
            Directory.CreateDirectory(path);
        }

        public void DeleteFile(string path)
        {
            File.Delete(path);
        }

        public void DeleteDirectory(string path, bool recursive = false)
        {
            Directory.Delete(path, recursive);
        }

        public string GetTempFileName()
        {
            return Path.GetTempFileName();
        }

        public string GetTempPath()
        {
            return Path.GetTempPath();
        }
    }
}
