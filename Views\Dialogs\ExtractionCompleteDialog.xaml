<Window x:Class="HaoZip.Views.Dialogs.ExtractionCompleteDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="解压完成"
        Height="220" Width="450"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False"
        Background="{StaticResource RetroBackground}"
        WindowStyle="SingleBorderWindow">
    
    <Border Background="{StaticResource RetroBackground}"
            BorderBrush="{StaticResource RetroBorder}"
            BorderThickness="2"
            Padding="20">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 成功图标和标题 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                <TextBlock Text="✅" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="解压完成！" 
                           FontSize="18" 
                           FontWeight="Bold"
                           Foreground="{StaticResource RetroText}"
                           VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 消息文本 -->
            <TextBlock Grid.Row="1" 
                       Name="MessageText"
                       Text="文件已成功解压到目标文件夹。"
                       FontSize="14"
                       Foreground="{StaticResource RetroText}"
                       TextWrapping="Wrap"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"/>

            <!-- 文件夹路径 -->
            <Border Grid.Row="2" 
                    Background="{StaticResource RetroAccent}"
                    BorderBrush="{StaticResource RetroBorder}"
                    BorderThickness="1"
                    Padding="10,5"
                    Margin="0,0,0,15">
                <TextBlock Name="FolderPathText"
                           Text=""
                           FontSize="12"
                           Foreground="{StaticResource RetroText}"
                           TextWrapping="Wrap"/>
            </Border>

            <!-- 询问是否打开文件夹 -->
            <TextBlock Grid.Row="3"
                       Text="是否打开目标文件夹？"
                       FontSize="14"
                       FontWeight="Bold"
                       Foreground="{StaticResource RetroText}"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"/>

            <!-- 记住选择复选框 -->
            <CheckBox Grid.Row="4"
                      Name="RememberChoiceCheckBox"
                      Content="记住我的选择，下次不再询问"
                      FontSize="12"
                      Foreground="{StaticResource RetroText}"
                      HorizontalAlignment="Center"
                      Margin="0,5,0,15"/>

            <!-- 按钮 -->
            <StackPanel Grid.Row="5"
                        Orientation="Horizontal"
                        HorizontalAlignment="Center">

                <Button Name="OpenFolderButton"
                        Content="🗂️ 打开文件夹"
                        Width="120"
                        Height="35"
                        Style="{StaticResource RetroButtonStyle}"
                        Click="OpenFolderButton_Click"
                        Margin="0,0,15,0"/>

                <Button Name="CloseButton"
                        Content="❌ 关闭"
                        Width="80"
                        Height="35"
                        Style="{StaticResource RetroButtonStyle}"
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
