<Window x:Class="HaoZip.Views.Dialogs.ExtractionCompleteDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="解压完成"
        Height="280" Width="450"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False"
        Background="#FF2D2D30"
        WindowStyle="SingleBorderWindow">

    <Border Background="#FF2D2D30"
            BorderBrush="#FF007ACC"
            BorderThickness="2"
            Padding="15">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 成功图标和标题 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5,0,10">
                <TextBlock Text="✅" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="解压完成！"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="White"
                           VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 消息文本 -->
            <TextBlock Grid.Row="1"
                       Name="MessageText"
                       Text="文件已成功解压到目标文件夹。"
                       FontSize="13"
                       Foreground="White"
                       TextWrapping="Wrap"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,8"/>

            <!-- 文件夹路径 -->
            <Border Grid.Row="2"
                    Background="#FF3E3E42"
                    BorderBrush="#FF007ACC"
                    BorderThickness="1"
                    Padding="8,4"
                    Margin="0,0,0,10">
                <TextBlock Name="FolderPathText"
                           Text=""
                           FontSize="10"
                           Foreground="White"
                           TextWrapping="Wrap"/>
            </Border>

            <!-- 询问是否打开文件夹 -->
            <TextBlock Grid.Row="3"
                       Text="是否打开目标文件夹？"
                       FontSize="13"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,8"/>

            <!-- 记住选择复选框 -->
            <CheckBox Grid.Row="4"
                      Name="RememberChoiceCheckBox"
                      Content="记住我的选择，下次不再询问"
                      FontSize="11"
                      Foreground="White"
                      HorizontalAlignment="Center"
                      Margin="0,5,0,15"/>

            <!-- 按钮 -->
            <StackPanel Grid.Row="5"
                        Orientation="Horizontal"
                        HorizontalAlignment="Center"
                        Margin="0,10,0,0">

                <Button Name="OpenFolderButton"
                        Content="🗂️ 打开文件夹"
                        Width="120"
                        Height="32"
                        Click="OpenFolderButton_Click"
                        Margin="0,0,15,0"
                        Background="#FF4CAF50"
                        Foreground="White"
                        BorderThickness="1"
                        BorderBrush="#FF388E3C"/>

                <Button Name="CloseButton"
                        Content="❌ 关闭"
                        Width="80"
                        Height="32"
                        Click="CloseButton_Click"
                        Background="#FF757575"
                        Foreground="White"
                        BorderThickness="1"
                        BorderBrush="#FF424242"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
