using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Windows;
using HaoZip.Services;

namespace HaoZip.Views.Dialogs
{
    public partial class SystemIntegrationDialog : Window
    {
        private readonly IRegistryService _registryService;
        private readonly string _executablePath;

        public SystemIntegrationDialog()
        {
            InitializeComponent();
            
            _registryService = ServiceLocator.GetService<IRegistryService>();
            _executablePath = Environment.ProcessPath ?? AppContext.BaseDirectory + "HaoZip.exe";
            
            LoadCurrentStatus();
        }

        private void LoadCurrentStatus()
        {
            try
            {
                var isZipDefault = _registryService.IsRegisteredAsDefault(".zip");
                SetAsDefaultCheckBox.IsChecked = isZipDefault;
                
                CompressHereCheckBox.IsChecked = true;
                ExtractHereCheckBox.IsChecked = true;
                ExtractToFolderCheckBox.IsChecked = true;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"Error checking status: {ex.Message}";
            }
        }

        private void InstallContextMenu_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsRunningAsAdministrator())
                {
                    MessageBox.Show("Administrator privileges are required to install context menu integration.", 
                        "Administrator Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CompressHereCheckBox.IsChecked == true)
                {
                    _registryService.RegisterContextMenu("*", "Compress with HaoZip", _executablePath);
                    _registryService.RegisterContextMenu("Directory", "Compress with HaoZip", _executablePath);
                }

                if (ExtractHereCheckBox.IsChecked == true)
                {
                    _registryService.RegisterContextMenu("HaoZip.Archive", "Extract Here", $"{_executablePath} /extracthere");
                }

                if (ExtractToFolderCheckBox.IsChecked == true)
                {
                    _registryService.RegisterContextMenu("HaoZip.Archive", "Extract to Folder", $"{_executablePath} /extractto");
                }

                MessageBox.Show("Context menu integration installed successfully.", "Success", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                CheckStatus_Click(null, null);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to install context menu integration: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UninstallContextMenu_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsRunningAsAdministrator())
                {
                    MessageBox.Show("Administrator privileges are required to uninstall context menu integration.", 
                        "Administrator Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _registryService.UnregisterContextMenu("*", "Compress with HaoZip");
                _registryService.UnregisterContextMenu("Directory", "Compress with HaoZip");
                _registryService.UnregisterContextMenu("HaoZip.Archive", "Extract Here");
                _registryService.UnregisterContextMenu("HaoZip.Archive", "Extract to Folder");

                MessageBox.Show("Context menu integration uninstalled successfully.", "Success", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                CheckStatus_Click(null, null);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to uninstall context menu integration: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RegisterAssociations_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsRunningAsAdministrator())
                {
                    MessageBox.Show("Administrator privileges are required to register file associations.", 
                        "Administrator Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var iconPath = Path.ChangeExtension(_executablePath, ".ico");
                
                _registryService.RegisterFileAssociation(".zip", "HaoZip.Archive", "ZIP Archive", iconPath, _executablePath);
                _registryService.RegisterFileAssociation(".7z", "HaoZip.Archive", "7-Zip Archive", iconPath, _executablePath);
                _registryService.RegisterFileAssociation(".rar", "HaoZip.Archive", "RAR Archive", iconPath, _executablePath);
                _registryService.RegisterFileAssociation(".tar", "HaoZip.Archive", "TAR Archive", iconPath, _executablePath);
                _registryService.RegisterFileAssociation(".gz", "HaoZip.Archive", "GZIP Archive", iconPath, _executablePath);
                _registryService.RegisterFileAssociation(".bz2", "HaoZip.Archive", "BZIP2 Archive", iconPath, _executablePath);

                MessageBox.Show("File associations registered successfully.", "Success", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                CheckStatus_Click(null, null);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to register file associations: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UnregisterAssociations_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsRunningAsAdministrator())
                {
                    MessageBox.Show("Administrator privileges are required to unregister file associations.", 
                        "Administrator Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _registryService.UnregisterFileAssociation(".zip", "HaoZip.Archive");
                _registryService.UnregisterFileAssociation(".7z", "HaoZip.Archive");
                _registryService.UnregisterFileAssociation(".rar", "HaoZip.Archive");
                _registryService.UnregisterFileAssociation(".tar", "HaoZip.Archive");
                _registryService.UnregisterFileAssociation(".gz", "HaoZip.Archive");
                _registryService.UnregisterFileAssociation(".bz2", "HaoZip.Archive");

                MessageBox.Show("File associations unregistered successfully.", "Success", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                CheckStatus_Click(null, null);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to unregister file associations: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetDefault_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!IsRunningAsAdministrator())
                {
                    MessageBox.Show("Administrator privileges are required to set as default program.", 
                        "Administrator Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var extensions = new[] { ".zip", ".7z", ".rar", ".tar", ".gz", ".bz2" };
                _registryService.SetAsDefaultProgram("HaoZip.Archive", extensions);

                MessageBox.Show("HaoZip has been set as the default archive program.", "Success", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                CheckStatus_Click(null, null);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to set as default program: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenDefaultApps_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "ms-settings:defaultapps",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Default Apps settings: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CheckStatus_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var status = "Current Integration Status:\n\n";
                
                var isZipDefault = _registryService.IsRegisteredAsDefault(".zip");
                status += $"• ZIP files: {(isZipDefault ? "HaoZip is default" : "Not default")}\n";
                
                var is7zDefault = _registryService.IsRegisteredAsDefault(".7z");
                status += $"• 7Z files: {(is7zDefault ? "HaoZip is default" : "Not default")}\n";
                
                var isRarDefault = _registryService.IsRegisteredAsDefault(".rar");
                status += $"• RAR files: {(isRarDefault ? "HaoZip is default" : "Not default")}\n";
                
                status += $"\nAdministrator privileges: {(IsRunningAsAdministrator() ? "Available" : "Not available")}";
                
                StatusTextBlock.Text = status;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"Error checking status: {ex.Message}";
            }
        }

        private bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
