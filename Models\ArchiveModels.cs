using System;
using System.ComponentModel;

namespace HaoZip.Models
{
    public enum CompressionFormat
    {
        Zip,
        Tar,
        GZip,
        BZip2
    }

    public enum CompressionLevel
    {
        Store = 0,
        Fastest = 1,
        Fast = 3,
        Normal = 5,
        Maximum = 7,
        Ultra = 9
    }

    public class ArchiveEntry : INotifyPropertyChanged
    {
        private bool _isSelected;

        public string Name { get; set; }
        public string FullPath { get; set; }
        public long Size { get; set; }
        public long CompressedSize { get; set; }
        public DateTime ModifiedTime { get; set; }
        public bool IsDirectory { get; set; }
        public string Type { get; set; }
        public double CompressionRatio => Size > 0 ? (double)CompressedSize / Size * 100 : 0;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        public string SizeFormatted => FormatFileSize(Size);
        public string CompressedSizeFormatted => FormatFileSize(CompressedSize);

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class CompressionSettings
    {
        public CompressionFormat DefaultFormat { get; set; } = CompressionFormat.Zip;
        public CompressionLevel DefaultLevel { get; set; } = CompressionLevel.Normal;
        public string TempDirectory { get; set; } = System.IO.Path.GetTempPath();
        public bool DeleteSourceAfterCompression { get; set; } = false;
        public bool ShowProgressDialog { get; set; } = true;
        public bool IntegrateWithExplorer { get; set; } = true;
        public bool SetAsDefaultProgram { get; set; } = false;
        public bool AskToOpenFolderAfterExtraction { get; set; } = true;
        public bool AlwaysOpenFolderAfterExtraction { get; set; } = false;
    }
}
