using System;
using System.Windows;
using HaoZip.Services;
using HaoZip.Utils;

namespace HaoZip
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            DispatcherUnhandledException += OnDispatcherUnhandledException;
            
            InitializeServices();
            
            if (e.Args.Length > 0)
            {
                HandleCommandLineArgs(e.Args);
            }
        }

        private void InitializeServices()
        {
            ServiceLocator.Initialize();

            var loggingService = ServiceLocator.GetService<ILoggingService>();
            ExceptionHandler.Initialize(loggingService);

            loggingService.LogInfo("HaoZip application started");
        }

        private void HandleCommandLineArgs(string[] args)
        {
            var mainWindow = Current.MainWindow as Views.MainWindow;
            if (mainWindow != null && args.Length > 0)
            {
                mainWindow.HandleFileArgument(args[0]);
            }
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            ExceptionHandler.HandleException(e.ExceptionObject as Exception, "Unhandled application exception");
        }

        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            ExceptionHandler.HandleException(e.Exception, "Unhandled dispatcher exception");
            e.Handled = true;
        }
    }
}
