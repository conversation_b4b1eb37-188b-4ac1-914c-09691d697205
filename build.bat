@echo off
echo Building HaoZip...

REM Clean previous builds
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

REM Restore packages
echo Restoring NuGet packages...
dotnet restore

REM Build Debug version
echo Building Debug version...
dotnet build -c Debug

REM Build Release version
echo Building Release version...
dotnet build -c Release

REM Publish self-contained version
echo Publishing self-contained version...
dotnet publish -c Release -r win-x64 --self-contained true -o "publish\win-x64"

echo Build completed successfully!
echo.
echo Debug build: bin\Debug\net6.0-windows\
echo Release build: bin\Release\net6.0-windows\
echo Published version: publish\win-x64\
echo.
pause
