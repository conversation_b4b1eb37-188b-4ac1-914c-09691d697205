<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 复古游戏机配色方案 - 优化版 -->
    <SolidColorBrush x:Key="RetroBackground" Color="#1A1A2E"/>
    <SolidColorBrush x:Key="RetroPanel" Color="#16213E"/>
    <SolidColorBrush x:Key="RetroAccent" Color="#0F3460"/>
    <SolidColorBrush x:Key="RetroHighlight" Color="#FF6B35"/>
    <SolidColorBrush x:Key="RetroSecondary" Color="#FFD23F"/>
    <SolidColorBrush x:Key="RetroSuccess" Color="#00FF88"/>
    <SolidColorBrush x:Key="RetroText" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="RetroTextDark" Color="#CCCCCC"/>
    <SolidColorBrush x:Key="RetroBorder" Color="#4A5568"/>

    <!-- 按钮专用配色 - 更鲜艳的街机风格 -->
    <SolidColorBrush x:Key="RetroButtonNormal" Color="#FF4757"/>
    <SolidColorBrush x:Key="RetroButtonHover" Color="#FF3742"/>
    <SolidColorBrush x:Key="RetroButtonPressed" Color="#FF2731"/>
    <SolidColorBrush x:Key="RetroButtonText" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="RetroButtonBorder" Color="#FF6B7A"/>
    <SolidColorBrush x:Key="RetroButtonShadow" Color="#2C2C54"/>
    <SolidColorBrush x:Key="RetroButtonGlow" Color="#FF9FF3"/>

    <!-- 特殊按钮配色 -->
    <SolidColorBrush x:Key="RetroButtonSuccess" Color="#2ED573"/>
    <SolidColorBrush x:Key="RetroButtonWarning" Color="#FFA502"/>
    <SolidColorBrush x:Key="RetroButtonInfo" Color="#3742FA"/>

    <!-- 复古游戏机按钮样式 - 简化稳定版 -->
    <Style x:Key="RetroButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource RetroButtonNormal}"/>
        <Setter Property="Foreground" Value="{StaticResource RetroButtonText}"/>
        <Setter Property="BorderThickness" Value="3"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroButtonBorder}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei, SimHei, Consolas, 'Courier New', monospace"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- 阴影层 -->
                        <Border x:Name="ShadowBorder"
                                Background="#2C2C54"
                                Margin="3,3,0,0"
                                Opacity="0.6"/>

                        <!-- 主按钮 -->
                        <Border x:Name="MainBorder"
                                Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                Margin="0,0,3,3">

                            <!-- 高光效果 -->
                            <Border BorderThickness="2"
                                    BorderBrush="#40FFFFFF"
                                    Margin="1">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"
                                                TextElement.Foreground="{TemplateBinding Foreground}"/>
                            </Border>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 鼠标悬停效果 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MainBorder" Property="Background" Value="#FF3742"/>
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="#FFD23F"/>
                        </Trigger>

                        <!-- 按下效果 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="MainBorder" Property="Background" Value="#FF2731"/>
                            <Setter TargetName="MainBorder" Property="Margin" Value="2,2,1,1"/>
                            <Setter TargetName="ShadowBorder" Property="Margin" Value="1,1,2,2"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="MainBorder" Property="Background" Value="#666666"/>
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="#888888"/>
                            <Setter Property="Foreground" Value="#AAAAAA"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 成功按钮样式（绿色） -->
    <Style x:Key="RetroSuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource RetroButtonStyle}">
        <Setter Property="Background" Value="{StaticResource RetroButtonSuccess}"/>
        <Setter Property="BorderBrush" Value="#4ECDC4"/>
    </Style>

    <!-- 警告按钮样式（橙色） -->
    <Style x:Key="RetroWarningButtonStyle" TargetType="Button" BasedOn="{StaticResource RetroButtonStyle}">
        <Setter Property="Background" Value="{StaticResource RetroButtonWarning}"/>
        <Setter Property="BorderBrush" Value="#FFB84D"/>
    </Style>

    <!-- 信息按钮样式（蓝色） -->
    <Style x:Key="RetroInfoButtonStyle" TargetType="Button" BasedOn="{StaticResource RetroButtonStyle}">
        <Setter Property="Background" Value="{StaticResource RetroButtonInfo}"/>
        <Setter Property="BorderBrush" Value="#5A67D8"/>
    </Style>

    <!-- 小型按钮样式 -->
    <Style x:Key="RetroSmallButtonStyle" TargetType="Button" BasedOn="{StaticResource RetroButtonStyle}">
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="Margin" Value="2"/>
    </Style>

    <!-- 兼容性别名 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource RetroButtonStyle}"/>

    <!-- 复古游戏机文本框样式 -->
    <Style x:Key="RetroTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="#000000"/>
        <Setter Property="Foreground" Value="#00FF00"/>
        <Setter Property="BorderThickness" Value="3"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroBorder}"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei, SimHei, Consolas, 'Courier New', monospace"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Grid>
                        <!-- 外层阴影 -->
                        <Border Background="#000000"
                                Margin="2,2,0,0"
                                Opacity="0.5"/>
                        <!-- 主边框 -->
                        <Border x:Name="MainBorder"
                                Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <!-- 内层凹陷效果 -->
                            <Border BorderThickness="1"
                                    BorderBrush="#333333"
                                    Margin="1">
                                <ScrollViewer x:Name="PART_ContentHost"
                                            Margin="{TemplateBinding Padding}"
                                            Background="Transparent"/>
                            </Border>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="{StaticResource RetroHighlight}"/>
                            <Setter Property="Foreground" Value="#FFFF00"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#1A1A1A"/>
                            <Setter Property="Foreground" Value="#666666"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 兼容性别名 -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource RetroTextBoxStyle}"/>

    <!-- 复古游戏机进度条样式 -->
    <Style x:Key="RetroProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="24"/>
        <Setter Property="Background" Value="#000000"/>
        <Setter Property="Foreground" Value="{StaticResource RetroSuccess}"/>
        <Setter Property="BorderThickness" Value="3"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroBorder}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Grid>
                        <!-- 外层阴影 -->
                        <Border Background="#000000"
                                Margin="2,2,0,0"
                                Opacity="0.5"/>
                        <!-- 主边框 -->
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <!-- 内层凹陷效果 -->
                            <Border BorderThickness="1"
                                    BorderBrush="#333333"
                                    Margin="1">
                                <Grid Margin="2">
                                    <!-- 背景网格效果 -->
                                    <Rectangle Opacity="0.3">
                                        <Rectangle.Fill>
                                            <DrawingBrush Viewport="0,0,4,4" ViewportUnits="Absolute" TileMode="Tile">
                                                <DrawingBrush.Drawing>
                                                    <GeometryDrawing Brush="#003300">
                                                        <GeometryDrawing.Geometry>
                                                            <RectangleGeometry Rect="0,0,2,2"/>
                                                        </GeometryDrawing.Geometry>
                                                    </GeometryDrawing>
                                                </DrawingBrush.Drawing>
                                            </DrawingBrush>
                                        </Rectangle.Fill>
                                    </Rectangle>
                                    <!-- 进度指示器 -->
                                    <Rectangle Name="PART_Indicator"
                                             Fill="{TemplateBinding Foreground}"
                                             HorizontalAlignment="Left">
                                        <Rectangle.Effect>
                                            <DropShadowEffect Color="#00FF00"
                                                            BlurRadius="3"
                                                            ShadowDepth="0"
                                                            Opacity="0.8"/>
                                        </Rectangle.Effect>
                                    </Rectangle>
                                </Grid>
                            </Border>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 兼容性别名 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource RetroProgressBarStyle}"/>

    <!-- 复古游戏机菜单样式 -->
    <Style x:Key="RetroMenuStyle" TargetType="Menu">
        <Setter Property="Background" Value="{StaticResource RetroPanel}"/>
        <Setter Property="Foreground" Value="{StaticResource RetroText}"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei, SimHei, Consolas, 'Courier New', monospace"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="BorderThickness" Value="0,0,0,3"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroHighlight}"/>
    </Style>

    <!-- 复古游戏机菜单项样式 -->
    <Style x:Key="RetroMenuItemStyle" TargetType="MenuItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource RetroText}"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei, SimHei, Consolas, 'Courier New', monospace"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="MenuItem">
                    <Border x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderThickness="1"
                            BorderBrush="Transparent">
                        <Grid>
                            <ContentPresenter ContentSource="Header"
                                            Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="Center"/>
                            <Popup x:Name="PART_Popup"
                                   Placement="Bottom"
                                   IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   AllowsTransparency="True">
                                <Border Background="{StaticResource RetroPanel}"
                                        BorderBrush="{StaticResource RetroBorder}"
                                        BorderThickness="2">
                                    <StackPanel IsItemsHost="True"/>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsHighlighted" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource RetroHighlight}"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource RetroSecondary}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource RetroAccent}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 复古游戏机工具栏样式 -->
    <Style x:Key="RetroToolBarStyle" TargetType="ToolBar">
        <Setter Property="Background" Value="{StaticResource RetroAccent}"/>
        <Setter Property="BorderThickness" Value="0,0,0,2"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroHighlight}"/>
    </Style>

    <!-- 复古游戏机状态栏样式 -->
    <Style x:Key="RetroStatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="{StaticResource RetroPanel}"/>
        <Setter Property="Foreground" Value="{StaticResource RetroText}"/>
        <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="BorderThickness" Value="0,2,0,0"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroHighlight}"/>
    </Style>

    <!-- 复古游戏机数据网格样式 -->
    <Style x:Key="RetroDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="{StaticResource RetroBackground}"/>
        <Setter Property="Foreground" Value="{StaticResource RetroText}"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroBorder}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="GridLinesVisibility" Value="All"/>
        <Setter Property="HorizontalGridLinesBrush" Value="#2C3E50"/>
        <Setter Property="VerticalGridLinesBrush" Value="#2C3E50"/>
        <Setter Property="RowBackground" Value="#1A1A2E"/>
        <Setter Property="AlternatingRowBackground" Value="#16213E"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
    </Style>

    <!-- 复古游戏机数据网格列标题样式 -->
    <Style x:Key="RetroDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="{StaticResource RetroAccent}"/>
        <Setter Property="Foreground" Value="{StaticResource RetroText}"/>
        <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="BorderBrush" Value="{StaticResource RetroBorder}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridColumnHeader">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Left"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
