﻿#pragma checksum "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8AE64DC2DCE4786ADD2CBDE8CEBA87CB78BD1BDA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HaoZip.Views.Dialogs {
    
    
    /// <summary>
    /// SystemIntegrationDialog
    /// </summary>
    public partial class SystemIntegrationDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompressHereCheckBox;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExtractHereCheckBox;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExtractToFolderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InstallContextMenuButton;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UninstallContextMenuButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterAssociationsButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UnregisterAssociationsButton;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SetAsDefaultCheckBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SetDefaultButton;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenDefaultAppsButton;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckStatusButton;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HaoZip;V1.0.0.0;component/views/dialogs/systemintegrationdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CompressHereCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.ExtractHereCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 3:
            this.ExtractToFolderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.InstallContextMenuButton = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.InstallContextMenuButton.Click += new System.Windows.RoutedEventHandler(this.InstallContextMenu_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.UninstallContextMenuButton = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.UninstallContextMenuButton.Click += new System.Windows.RoutedEventHandler(this.UninstallContextMenu_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RegisterAssociationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.RegisterAssociationsButton.Click += new System.Windows.RoutedEventHandler(this.RegisterAssociations_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.UnregisterAssociationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.UnregisterAssociationsButton.Click += new System.Windows.RoutedEventHandler(this.UnregisterAssociations_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SetAsDefaultCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.SetDefaultButton = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.SetDefaultButton.Click += new System.Windows.RoutedEventHandler(this.SetDefault_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.OpenDefaultAppsButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.OpenDefaultAppsButton.Click += new System.Windows.RoutedEventHandler(this.OpenDefaultApps_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.CheckStatusButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.CheckStatusButton.Click += new System.Windows.RoutedEventHandler(this.CheckStatus_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\..\..\..\Views\Dialogs\SystemIntegrationDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

