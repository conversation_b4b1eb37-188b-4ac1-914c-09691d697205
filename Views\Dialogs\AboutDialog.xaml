<Window x:Class="HaoZip.Views.Dialogs.AboutDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="About HaoZip" 
        Height="350" Width="450"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Margin="30" HorizontalAlignment="Center" VerticalAlignment="Center">
            <Image Source="../../Resources/haozip.ico" Width="64" Height="64" Margin="0,0,0,20"/>
            
            <TextBlock Text="HaoZip" FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center" Margin="0,0,0,10"/>
            
            <TextBlock Text="Professional Archive Manager" FontSize="14" 
                       HorizontalAlignment="Center" Margin="0,0,0,20" 
                       Foreground="#FF666666"/>
            
            <TextBlock Name="VersionLabel" Text="Version 1.0.0" FontSize="12" 
                       HorizontalAlignment="Center" Margin="0,0,0,10"/>
            
            <TextBlock Text="Copyright © 2025 HaoZip Technologies" FontSize="12" 
                       HorizontalAlignment="Center" Margin="0,0,0,20"/>
            
            <TextBlock Text="Advanced compression and decompression software for Windows" 
                       FontSize="12" HorizontalAlignment="Center" 
                       TextWrapping="Wrap" TextAlignment="Center" 
                       Margin="0,0,0,20"/>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="Built with " FontSize="12" VerticalAlignment="Center"/>
                <TextBlock Text="❤️" FontSize="14" VerticalAlignment="Center" Margin="2,0"/>
                <TextBlock Text=" using C# and WPF" FontSize="12" VerticalAlignment="Center"/>
            </StackPanel>
        </StackPanel>

        <Border Grid.Row="1" Background="#FFF0F0F0" BorderBrush="#FFCCCCCC" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,10">
                <Button Name="OkButton" Content="OK" Width="80" 
                        Style="{StaticResource ModernButtonStyle}" 
                        Click="OkButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
