using System;
using System.Windows;
using HaoZip.Services;

namespace HaoZip.Views.Dialogs
{
    public partial class ProgressDialog : Window
    {
        private DateTime _startTime;
        private long _lastProcessedBytes;
        private DateTime _lastUpdateTime;
        
        public event EventHandler CancelRequested;

        public ProgressDialog()
        {
            InitializeComponent();
            _startTime = DateTime.Now;
            _lastUpdateTime = DateTime.Now;
        }

        public void UpdateProgress(ProgressInfo info)
        {
            Dispatcher.Invoke(() =>
            {
                StatusLabel.Text = info.Status ?? "处理中...";
                CurrentFileLabel.Text = string.IsNullOrEmpty(info.CurrentFile) ? "等待文件中..." : $"正在处理: {info.CurrentFile}";

                var percentage = Math.Min(100, Math.Max(0, info.PercentComplete));
                MainProgressBar.Value = percentage;
                PercentageLabel.Text = $"{percentage:F1}%";

                FilesProgressLabel.Text = $"文件: {info.ProcessedFiles} / {info.TotalFiles}";
                SizeProgressLabel.Text = $"大小: {FormatFileSize(info.ProcessedBytes)} / {FormatFileSize(info.TotalBytes)}";
                
                UpdateSpeed(info.ProcessedBytes);
            });
        }

        private void UpdateSpeed(long processedBytes)
        {
            var now = DateTime.Now;
            var timeDiff = (now - _lastUpdateTime).TotalSeconds;
            
            if (timeDiff >= 1.0)
            {
                var bytesDiff = processedBytes - _lastProcessedBytes;
                var speed = bytesDiff / timeDiff;
                
                SpeedLabel.Text = $"{FormatFileSize((long)speed)}/S";
                
                _lastProcessedBytes = processedBytes;
                _lastUpdateTime = now;
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            CancelButton.IsEnabled = false;
            CancelButton.Content = "⏹️ 正在中止...";
            StatusLabel.Text = "正在取消操作...";

            CancelRequested?.Invoke(this, EventArgs.Empty);
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (CancelButton.IsEnabled)
            {
                e.Cancel = true;
                CancelButton_Click(null, null);
            }
            else
            {
                base.OnClosing(e);
            }
        }
    }
}
